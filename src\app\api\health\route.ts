import { NextResponse } from 'next/server'
import { checkDatabaseHealth } from '@/lib/prisma'

export async function GET() {
  try {
    // 检查数据库连接
    const dbHealth = await checkDatabaseHealth()

    // 基础健康检查
    const health = {
      status: dbHealth.healthy ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      version: process.env['npm_package_version'] || '1.0.0',
      environment: process.env.NODE_ENV,
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      database: {
        status: dbHealth.healthy ? 'connected' : 'disconnected',
        error: dbHealth.error || null
      },
      services: {
        api: 'operational',
        database: dbHealth.healthy ? 'operational' : 'degraded'
      }
    }

    const statusCode = dbHealth.healthy ? 200 : 503
    return NextResponse.json(health, { status: statusCode })
  } catch (error) {
    return NextResponse.json(
      {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: 'Health check failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
