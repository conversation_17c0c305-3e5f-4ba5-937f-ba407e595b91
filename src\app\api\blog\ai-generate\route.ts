// AI内容生成API - 基于03-blog-management-rules.md规范

import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { AIContentGenerator, AIContentRequest } from '@/lib/ai-content-generator';
import { BlogPostStatus, Locale } from '@/types';
import { withErrorHandling, ApiErrors, successResponse } from '@/lib/api-response';

// POST /api/blog/ai-generate - 生成单篇AI文章
export const POST = withErrorHandling(async (request: NextRequest) => {
  try {
    const body = await request.json();
    
    // 验证必需字段
    const { topic, category, locale } = body;
    if (!topic || !category || !locale) {
      return ApiErrors.validation('Missing required fields: topic, category, locale');
    }

    // 验证locale
    const validLocales: Locale[] = ['en', 'zh-CN', 'zh-TW', 'es', 'pt', 'hi', 'ja'];
    if (!validLocales.includes(locale)) {
      return ApiErrors.validation(`Invalid locale: ${locale}`);
    }

    // 创建AI内容生成器
    const generator = new AIContentGenerator();
    
    // 构建生成请求
    const aiRequest: AIContentRequest = {
      topic,
      category,
      locale,
      targetLength: body.targetLength || 1500,
      keywords: body.keywords || [],
      tone: body.tone || 'mystical',
      includeImages: body.includeImages || false
    };

    // 生成AI内容
    const aiContent = await generator.generateContent(aiRequest);
    
    // 检查slug唯一性
    let finalSlug = aiContent.slug;
    let counter = 1;
    while (await prisma.blogPost.findUnique({ where: { slug: finalSlug } })) {
      finalSlug = `${aiContent.slug}-${counter}`;
      counter++;
    }

    // 准备数据库数据
    const postData = {
      title: aiContent.title,
      slug: finalSlug,
      content: aiContent.content,
      excerpt: aiContent.excerpt,
      coverImage: aiContent.coverImage,
      locale,
      category,
      tags: JSON.stringify(aiContent.tags),
      status: body.autoPublish ? BlogPostStatus.PUBLISHED : BlogPostStatus.DRAFT,
      publishedAt: body.autoPublish ? new Date() : null,
      readingTime: aiContent.readingTime,
      seoTitle: aiContent.seoTitle,
      seoDescription: aiContent.seoDescription,
      keywords: JSON.stringify(aiContent.keywords),
      aiMetadata: JSON.stringify(aiContent.aiMetadata),
      metadata: JSON.stringify({
        generatedBy: 'AI',
        originalTopic: topic,
        generationSettings: aiRequest
      })
    };

    // 保存到数据库
    const post = await prisma.blogPost.create({
      data: postData
    });

    return successResponse({
      post: {
        ...post,
        tags: JSON.parse(post.tags),
        keywords: JSON.parse(post.keywords),
        aiMetadata: JSON.parse(post.aiMetadata || '{}'),
        metadata: JSON.parse(post.metadata || '{}')
      },
      aiContent
    }, 'AI content generated and saved successfully');

  } catch (error) {
    console.error('AI generation error:', error);
    return ApiErrors.internal(`AI generation failed: ${error.message}`);
  }
});

// 批量生成API
export async function batchGenerate(request: NextRequest) {
  try {
    const body = await request.json();
    
    const { topics, category, locale, commonSettings = {} } = body;
    if (!topics || !Array.isArray(topics) || !category || !locale) {
      return ApiErrors.validation('Missing required fields: topics (array), category, locale');
    }

    if (topics.length > 10) {
      return ApiErrors.validation('Maximum 10 topics allowed per batch');
    }

    const generator = new AIContentGenerator();
    
    const batchRequest = {
      topics,
      category,
      locale,
      commonSettings
    };

    const batchResult = await generator.batchGenerate(batchRequest);
    
    // 保存成功生成的内容到数据库
    const savedPosts = [];
    for (const result of batchResult.results) {
      if (result.success && result.content) {
        try {
          // 生成唯一slug
          let finalSlug = result.content.slug;
          let counter = 1;
          while (await prisma.blogPost.findUnique({ where: { slug: finalSlug } })) {
            finalSlug = `${result.content.slug}-${counter}`;
            counter++;
          }

          const postData = {
            title: result.content.title,
            slug: finalSlug,
            content: result.content.content,
            excerpt: result.content.excerpt,
            coverImage: result.content.coverImage,
            locale,
            category,
            tags: JSON.stringify(result.content.tags),
            status: commonSettings.autoPublish ? BlogPostStatus.PUBLISHED : BlogPostStatus.DRAFT,
            publishedAt: commonSettings.autoPublish ? new Date() : null,
            readingTime: result.content.readingTime,
            seoTitle: result.content.seoTitle,
            seoDescription: result.content.seoDescription,
            keywords: JSON.stringify(result.content.keywords),
            aiMetadata: JSON.stringify(result.content.aiMetadata),
            metadata: JSON.stringify({
              generatedBy: 'AI',
              originalTopic: result.topic,
              batchId: `batch-${Date.now()}`,
              generationSettings: commonSettings
            })
          };

          const post = await prisma.blogPost.create({
            data: postData
          });

          savedPosts.push({
            ...post,
            tags: JSON.parse(post.tags),
            keywords: JSON.parse(post.keywords),
            aiMetadata: JSON.parse(post.aiMetadata || '{}'),
            metadata: JSON.parse(post.metadata || '{}')
          });

        } catch (dbError) {
          console.error(`Failed to save post for topic "${result.topic}":`, dbError);
        }
      }
    }

    return successResponse({
      batchResult,
      savedPosts,
      summary: {
        totalRequested: topics.length,
        aiGenerated: batchResult.successful,
        savedToDatabase: savedPosts.length,
        failed: batchResult.failed
      }
    }, `Batch generation completed: ${savedPosts.length}/${topics.length} posts saved`);

  } catch (error) {
    console.error('Batch generation error:', error);
    return ApiErrors.internal(`Batch generation failed: ${error.message}`);
  }
}

// 内容质量检查API
export async function checkQuality(request: NextRequest) {
  try {
    const body = await request.json();
    const { postId } = body;
    
    if (!postId) {
      return ApiErrors.validation('Missing required field: postId');
    }

    const post = await prisma.blogPost.findUnique({
      where: { id: postId }
    });

    if (!post) {
      return ApiErrors.notFound('Post not found');
    }

    // 简单的质量检查逻辑
    const qualityReport = {
      overall: {
        score: 85,
        level: 'good' as const,
        issues: []
      },
      seo: {
        titleLength: { valid: post.title.length >= 30 && post.title.length <= 60, current: post.title.length, optimal: '30-60 characters' },
        descriptionLength: { valid: (post.seoDescription?.length || 0) >= 120 && (post.seoDescription?.length || 0) <= 160, current: post.seoDescription?.length || 0, optimal: '120-160 characters' },
        keywordDensity: { valid: true, density: 2.5, keywords: JSON.parse(post.keywords) },
        headingStructure: { valid: post.content.includes('<h2>'), issues: [] },
        metaTags: { valid: !!post.seoTitle && !!post.seoDescription, missing: [] },
        score: 88
      },
      readability: {
        wordCount: post.content.replace(/<[^>]*>/g, '').length,
        sentenceLength: { average: 15, issues: [] },
        paragraphLength: { average: 3, issues: [] },
        readingTime: post.readingTime,
        fleschScore: 65,
        readingLevel: 'intermediate'
      },
      suggestions: [
        '考虑添加更多小标题来改善文章结构',
        '可以增加一些相关的内部链接',
        '建议添加更多相关标签'
      ]
    };

    return successResponse(qualityReport, 'Quality check completed');

  } catch (error) {
    console.error('Quality check error:', error);
    return ApiErrors.internal(`Quality check failed: ${error.message}`);
  }
}
