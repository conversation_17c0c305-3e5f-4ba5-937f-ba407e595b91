'use client';

import React from 'react';
import Link from 'next/link';
import { <PERSON>rk<PERSON>, ArrowR<PERSON>, Clock, Users } from 'lucide-react';
import { cn } from '@/lib/utils';
import { BlogPost } from '@/types';

interface TestRecommendationProps {
  post: BlogPost;
  className?: string;
}

// 根据博客分类推荐相关测试
const getRecommendedTests = (category: string) => {
  const testMap = {
    tarot: {
      title: '塔罗牌测试',
      description: '通过AI智能分析，获得专业的塔罗牌解读',
      url: '/tarot/test',
      icon: '🔮',
      estimatedTime: '5-8分钟',
      completedCount: '12,847'
    },
    astrology: {
      title: '星座性格测试',
      description: '深度分析你的星座特质和性格密码',
      url: '/astrology/test',
      icon: '⭐',
      estimatedTime: '3-5分钟',
      completedCount: '8,923'
    },
    numerology: {
      title: '数字命理测试',
      description: '解读你的生命数字，发现隐藏的人生密码',
      url: '/numerology/test',
      icon: '🔢',
      estimatedTime: '4-6分钟',
      completedCount: '6,754'
    },
    crystal: {
      title: '水晶能量测试',
      description: '找到最适合你的水晶，提升个人能量',
      url: '/crystal/test',
      icon: '💎',
      estimatedTime: '3-4分钟',
      completedCount: '5,432'
    },
    palmistry: {
      title: '手相解读测试',
      description: 'AI分析你的手相特征，解读命运线条',
      url: '/palmistry/test',
      icon: '✋',
      estimatedTime: '6-8分钟',
      completedCount: '4,321'
    },
    dreams: {
      title: '梦境解析测试',
      description: '解读你的梦境符号，发现潜意识信息',
      url: '/dreams/test',
      icon: '🌙',
      estimatedTime: '4-7分钟',
      completedCount: '3,876'
    }
  };

  return testMap[category as keyof typeof testMap] || testMap.tarot;
};

export function TestRecommendation({ post, className }: TestRecommendationProps) {
  const recommendedTest = getRecommendedTests(post.category);

  return (
    <div className={cn(
      'bg-gradient-to-br from-mystical-50 to-gold-50 dark:from-dark-800 dark:to-dark-700',
      'border border-mystical-200 dark:border-dark-600 rounded-xl p-6',
      'relative overflow-hidden',
      className
    )}>
      {/* 背景装饰 */}
      <div className="absolute top-0 right-0 w-32 h-32 opacity-10">
        <div className="text-6xl transform rotate-12">
          {recommendedTest.icon}
        </div>
      </div>

      {/* 主要内容 */}
      <div className="relative z-10">
        {/* 标题区域 */}
        <div className="flex items-center gap-3 mb-4">
          <div className="w-10 h-10 bg-mystical-500 rounded-full flex items-center justify-center">
            <Sparkles className="w-5 h-5 text-white" />
          </div>
          <div>
            <h3 className="text-lg font-bold text-mystical-900 dark:text-white">
              相关测试推荐
            </h3>
            <p className="text-sm text-mystical-600 dark:text-mystical-300">
              基于你正在阅读的内容
            </p>
          </div>
        </div>

        {/* 测试卡片 */}
        <div className="bg-white dark:bg-dark-800 rounded-lg p-5 border border-mystical-200 dark:border-dark-600 mb-4">
          <div className="flex items-start gap-4">
            <div className="text-3xl">{recommendedTest.icon}</div>
            <div className="flex-1">
              <h4 className="text-xl font-bold text-mystical-900 dark:text-white mb-2">
                {recommendedTest.title}
              </h4>
              <p className="text-mystical-600 dark:text-mystical-300 mb-4 leading-relaxed">
                {recommendedTest.description}
              </p>
              
              {/* 测试统计 */}
              <div className="flex items-center gap-6 text-sm text-mystical-500 dark:text-mystical-400 mb-4">
                <div className="flex items-center gap-1">
                  <Clock className="w-4 h-4" />
                  <span>{recommendedTest.estimatedTime}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Users className="w-4 h-4" />
                  <span>{recommendedTest.completedCount}人已测试</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* CTA按钮 */}
        <Link
          href={recommendedTest.url}
          className={cn(
            'w-full flex items-center justify-center gap-2 px-6 py-4',
            'bg-mystical-500 hover:bg-mystical-600 text-white font-semibold rounded-lg',
            'transition-all duration-300 hover:shadow-mystical-lg hover:scale-[1.02]',
            'group'
          )}
        >
          <span>立即开始免费测试</span>
          <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
        </Link>

        {/* 信任标识 */}
        <div className="flex items-center justify-center gap-4 mt-4 text-xs text-mystical-500 dark:text-mystical-400">
          <span>✓ 完全免费</span>
          <span>✓ AI智能分析</span>
          <span>✓ 专业解读</span>
          <span>✓ 即时结果</span>
        </div>
      </div>
    </div>
  );
}

// 内联测试推荐组件 - 用于文章内容中
export function InlineTestRecommendation({ post }: { post: BlogPost }) {
  const recommendedTest = getRecommendedTests(post.category);

  return (
    <div className={cn(
      'my-8 p-4 bg-mystical-50 dark:bg-dark-800 border-l-4 border-mystical-500 rounded-r-lg',
      'not-prose' // 防止prose样式影响
    )}>
      <div className="flex items-center gap-3 mb-3">
        <div className="text-2xl">{recommendedTest.icon}</div>
        <div>
          <h4 className="font-bold text-mystical-900 dark:text-white">
            想要更深入的了解？
          </h4>
          <p className="text-sm text-mystical-600 dark:text-mystical-300">
            尝试我们的{recommendedTest.title}
          </p>
        </div>
      </div>
      
      <Link
        href={recommendedTest.url}
        className={cn(
          'inline-flex items-center gap-2 px-4 py-2 text-sm font-medium',
          'bg-mystical-500 hover:bg-mystical-600 text-white rounded-lg',
          'transition-colors duration-200'
        )}
      >
        <span>免费测试</span>
        <ArrowRight className="w-4 h-4" />
      </Link>
    </div>
  );
}

// 相关文章推荐组件
export function RelatedPostsRecommendation({ 
  currentPost, 
  relatedPosts 
}: { 
  currentPost: BlogPost; 
  relatedPosts: BlogPost[] 
}) {
  if (relatedPosts.length === 0) return null;

  return (
    <div className="mt-12 pt-8 border-t border-mystical-200 dark:border-dark-700">
      <h3 className="text-2xl font-bold text-mystical-900 dark:text-white mb-6">
        相关文章推荐
      </h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {relatedPosts.slice(0, 4).map((post) => (
          <Link
            key={post.id}
            href={`/blog/${post.category}/${post.slug}`}
            className={cn(
              'group p-4 bg-white dark:bg-dark-800 rounded-lg border border-mystical-200 dark:border-dark-600',
              'hover:shadow-mystical-lg hover:border-mystical-300 dark:hover:border-dark-500',
              'transition-all duration-300'
            )}
          >
            <div className="flex gap-4">
              {post.coverImage && (
                <div className="w-20 h-20 bg-mystical-100 dark:bg-dark-700 rounded-lg overflow-hidden flex-shrink-0">
                  <img
                    src={post.coverImage}
                    alt={post.title}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                </div>
              )}
              <div className="flex-1 min-w-0">
                <h4 className="font-semibold text-mystical-900 dark:text-white mb-2 line-clamp-2 group-hover:text-mystical-600 dark:group-hover:text-mystical-300 transition-colors">
                  {post.title}
                </h4>
                <p className="text-sm text-mystical-600 dark:text-mystical-400 line-clamp-2">
                  {post.excerpt}
                </p>
                <div className="flex items-center gap-2 mt-2 text-xs text-mystical-500 dark:text-mystical-400">
                  <Clock className="w-3 h-3" />
                  <span>{post.readingTime} min read</span>
                </div>
              </div>
            </div>
          </Link>
        ))}
      </div>
    </div>
  );
}
