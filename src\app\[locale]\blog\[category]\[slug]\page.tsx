import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { getTranslations } from 'next-intl/server';
import { Suspense } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import {
  ReadingProgress,
  TableOfContents,
  MobileTableOfContents,
  generateTableOfContents,
  BlogEngagementButtons,
  ArticleFooter
} from '@/components/blog';
import { BlogPost, Locale } from '@/types';
import { Calendar, Clock, Eye, Heart, Share2, User, Tag, BookOpen } from 'lucide-react';
import { cn } from '@/lib/utils';
import { prisma } from '@/lib/prisma';

interface BlogPostPageProps {
  params: {
    locale: string;
    category: string;
    slug: string;
  };
}

export async function generateMetadata({ params }: BlogPostPageProps): Promise<Metadata> {
  const post = await getPostBySlug(params.category, params.slug, params.locale as Locale);
  
  if (!post) {
    return {
      title: 'Post Not Found',
    };
  }
  
  return {
    title: post.seo.title || post.title,
    description: post.seo.description || post.excerpt,
    keywords: post.seo.keywords,
    authors: [{ name: post.author.name }],
    openGraph: {
      title: post.seo.title || post.title,
      description: post.seo.description || post.excerpt,
      type: 'article',
      locale: params.locale,
      publishedTime: post.publishedAt?.toISOString(),
      modifiedTime: post.updatedAt.toISOString(),
      authors: [post.author.name],
      images: post.coverImage ? [{ url: post.coverImage, alt: post.title }] : [],
    },
    twitter: {
      card: 'summary_large_image',
      title: post.seo.title || post.title,
      description: post.seo.description || post.excerpt,
      images: post.coverImage ? [post.coverImage] : [],
    },
  };
}

// 实际的数据库查询函数
async function getPostBySlug(
  category: string,
  slug: string,
  locale: Locale
): Promise<BlogPost | null> {
  try {
    const post = await prisma.blogPost.findFirst({
      where: {
        slug,
        locale,
        status: 'PUBLISHED'
      },
      include: {
        views: true,
        comments: {
          where: { isApproved: true },
          orderBy: { createdAt: 'desc' }
        }
      }
    });

    if (!post) return null;

    // 解析JSON字段
    return {
      ...post,
      tags: JSON.parse(post.tags || '[]'),
      keywords: JSON.parse(post.keywords || '[]'),
      metadata: JSON.parse(post.metadata || '{}'),
      author: JSON.parse(post.metadata || '{}').author || {
        name: '神秘作者',
        avatar: '/images/avatars/default.jpg',
        bio: '专业的玄学研究者，致力于分享古老智慧与现代生活的结合。'
      }
    } as BlogPost;
  } catch (error) {
    console.error('Error fetching post:', error);
    return null;
  }
}

async function getRelatedPosts(
  postId: string,
  categorySlug: string,
  locale: Locale,
  limit: number = 3
): Promise<BlogPost[]> {
  try {
    const posts = await prisma.blogPost.findMany({
      where: {
        locale,
        status: 'PUBLISHED',
        category: categorySlug,
        NOT: { id: postId }
      },
      orderBy: { publishedAt: 'desc' },
      take: limit
    });

    return posts.map(post => ({
      ...post,
      tags: JSON.parse(post.tags || '[]'),
      keywords: JSON.parse(post.keywords || '[]'),
      metadata: JSON.parse(post.metadata || '{}'),
      author: JSON.parse(post.metadata || '{}').author || {
        name: '神秘作者',
        avatar: '/images/avatars/default.jpg'
      }
    })) as BlogPost[];
  } catch (error) {
    console.error('Error fetching related posts:', error);
    return [];
  }
}

export default async function BlogPostPage({ params }: BlogPostPageProps) {
  const t = await getTranslations({ locale: params.locale, namespace: 'blog' });
  const locale = params.locale as Locale;

  const post = await getPostBySlug(params.category, params.slug, locale);

  if (!post) {
    notFound();
  }

  const relatedPosts = await getRelatedPosts(post.id, params.category, locale);
  const tableOfContents = generateTableOfContents(post.content);

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat(locale, {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    }).format(date);
  };

  return (
    <div className="min-h-screen bg-white dark:bg-dark-900">
      {/* 阅读进度指示器 - 顶部进度条 */}
      <ReadingProgress target="article-content" variant="bar" />

      {/* 浮动阅读进度圆环 */}
      <ReadingProgress target="article-content" variant="circle" />

      {/* 文章头部 - 简化设计，专注内容 */}
      <header className="py-12 bg-white dark:bg-dark-900">
        <div className="max-w-[680px] mx-auto px-4">
          {/* 面包屑导航 */}
          <nav className="flex items-center space-x-2 text-sm text-mystical-500 dark:text-mystical-400 mb-8">
            <Link href="/blog" className="hover:text-mystical-600 dark:hover:text-mystical-300 transition-colors">
              {t('blog')}
            </Link>
            <span>·</span>
            <Link
              href={`/blog/${post.category.slug}`}
              className="hover:text-mystical-600 dark:hover:text-mystical-300 transition-colors"
            >
              {post.category.name}
            </Link>
          </nav>

          {/* 文章标题 - Medium风格 */}
          <h1 className={cn(
            "text-3xl md:text-4xl lg:text-5xl font-bold font-serif",
            "text-mystical-900 dark:text-white mb-6",
            "leading-[1.2] tracking-[-0.02em]"
          )}>
            {post.title}
          </h1>

          {/* 文章副标题/摘要 - Medium风格 */}
          <p className={cn(
            "text-lg md:text-xl text-mystical-600 dark:text-mystical-300",
            "mb-8 leading-[1.5] font-serif italic"
          )}>
            {post.excerpt}
          </p>

          {/* 作者信息区域 - Medium风格 */}
          <div className="flex items-center gap-4 pb-8 border-b border-mystical-200 dark:border-dark-700">
            {post.author.avatar && (
              <Image
                src={post.author.avatar}
                alt={post.author.name}
                width={48}
                height={48}
                className="rounded-full border-2 border-mystical-200 dark:border-dark-600"
              />
            )}
            <div className="flex-1">
              <p className="text-base font-semibold text-mystical-900 dark:text-white mb-1">
                {post.author.name}
              </p>
              <div className="flex items-center gap-4 text-sm text-mystical-500 dark:text-mystical-400">
                <div className="flex items-center gap-1">
                  <Calendar className="w-4 h-4" />
                  <span>{formatDate(post.publishedAt || post.createdAt)}</span>
                </div>
                <div className="flex items-center gap-1">
                  <BookOpen className="w-4 h-4" />
                  <span>{post.readingTime} min read</span>
                </div>
                <div className="flex items-center gap-1">
                  <Eye className="w-4 h-4" />
                  <span>{post.viewCount.toLocaleString()}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* 封面图片 - Medium风格 */}
      {post.coverImage && (
        <div className="mb-12">
          <div className="max-w-[680px] mx-auto px-4">
            <Image
              src={post.coverImage}
              alt={post.title}
              width={680}
              height={383}
              className="w-full aspect-[16/9] object-cover rounded-lg"
              priority
              sizes="680px"
            />
          </div>
        </div>
      )}

      {/* 主要内容区域 - Medium布局 */}
      <div className="relative">
        {/* 浮动目录 - 仅在桌面端显示 */}
        {tableOfContents.length > 0 && (
          <TableOfContents
            items={tableOfContents}
            variant="floating"
            className="hidden xl:block"
          />
        )}

        {/* 浮动互动按钮 - 仅在桌面端显示 */}
        <BlogEngagementButtons
          post={post}
          className="hidden xl:block"
        />

        {/* 文章内容容器 */}
        <div className="max-w-[680px] mx-auto px-4 pb-16">
          {/* 移动端目录 */}
          <MobileTableOfContents items={tableOfContents} className="mb-8 xl:hidden" />

          {/* 文章正文 - Medium标准排版 */}
          <article
            id="article-content"
            className={cn(
              "prose prose-lg max-w-none",
              // Medium标准字体和行高
              "prose-mystical dark:prose-invert",
              // 自定义样式覆盖
              "[&>p]:text-[1.25rem] [&>p]:leading-[1.75] [&>p]:text-mystical-800 dark:[&>p]:text-mystical-100",
              "[&>p]:font-serif [&>p]:tracking-[-0.003em] [&>p]:mb-6",
              // 首段特殊样式
              "[&>p:first-of-type]:text-[1.375rem] [&>p:first-of-type]:font-normal [&>p:first-of-type]:text-mystical-900 dark:[&>p:first-of-type]:text-white",
              // 标题样式
              "[&>h1]:text-[2.5rem] [&>h1]:font-bold [&>h1]:font-serif [&>h1]:leading-[1.2] [&>h1]:text-mystical-900 dark:[&>h1]:text-white [&>h1]:mt-12 [&>h1]:mb-6",
              "[&>h2]:text-[2rem] [&>h2]:font-bold [&>h2]:font-serif [&>h2]:leading-[1.3] [&>h2]:text-mystical-900 dark:[&>h2]:text-white [&>h2]:mt-10 [&>h2]:mb-4",
              "[&>h3]:text-[1.75rem] [&>h3]:font-semibold [&>h3]:font-serif [&>h3]:leading-[1.4] [&>h3]:text-mystical-800 dark:[&>h3]:text-mystical-100 [&>h3]:mt-8 [&>h3]:mb-4",
              // 引用块样式
              "[&>blockquote]:text-[1.375rem] [&>blockquote]:italic [&>blockquote]:font-serif [&>blockquote]:leading-[1.6]",
              "[&>blockquote]:text-mystical-700 dark:[&>blockquote]:text-mystical-200 [&>blockquote]:px-8 [&>blockquote]:py-6",
              "[&>blockquote]:bg-mystical-50 dark:[&>blockquote]:bg-dark-800 [&>blockquote]:border-l-4 [&>blockquote]:border-mystical-400",
              "[&>blockquote]:rounded-r-lg [&>blockquote]:my-8",
              // 列表样式
              "[&>ul>li]:text-[1.25rem] [&>ul>li]:leading-[1.75] [&>ul>li]:text-mystical-800 dark:[&>ul>li]:text-mystical-100 [&>ul>li]:mb-2",
              "[&>ol>li]:text-[1.25rem] [&>ol>li]:leading-[1.75] [&>ol>li]:text-mystical-800 dark:[&>ol>li]:text-mystical-100 [&>ol>li]:mb-2",
              // 链接样式
              "[&>p>a]:text-mystical-600 dark:[&>p>a]:text-mystical-400 [&>p>a]:underline [&>p>a]:decoration-mystical-300",
              "[&>p>a]:underline-offset-[0.2em] [&>p>a]:decoration-1 hover:[&>p>a]:text-mystical-700 dark:hover:[&>p>a]:text-mystical-300",
              // 图片样式
              "[&>p>img]:w-full [&>p>img]:rounded-lg [&>p>img]:my-8 [&>p>img]:shadow-mystical"
            )}
          >
            {/* 这里应该渲染Markdown内容 */}
            <div dangerouslySetInnerHTML={{ __html: post.content }} />
          </article>

          {/* 文章底部 */}
          <div className="mt-16">
            <ArticleFooter post={post} relatedPosts={relatedPosts} />
          </div>
        </div>
      </div>
    </div>
  );
}


