// 测试结果分享API
// Test Result Sharing API

import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { 
  successResponse, 
  ApiErrors, 
  withErrorHandling,
  parseJsonField
} from '@/lib/api-response'

interface RouteParams {
  params: {
    token: string
  }
}

// GET /api/tests/result/[token] - 获取分享的测试结果
export const GET = withErrorHandling(async (
  request: NextRequest,
  { params }: RouteParams
) => {
  const { token } = params
  
  if (!token) {
    return ApiErrors.validation('Share token is required')
  }
  
  // 查找测试结果
  const testResult = await prisma.testResult.findUnique({
    where: { 
      shareToken: token,
      isPublic: true // 只返回公开的结果
    },
    include: {
      user: {
        select: {
          id: true,
          username: true,
          avatar: true
        }
      }
    }
  })
  
  if (!testResult) {
    return ApiErrors.notFound('Test result not found or not public')
  }
  
  // 解析JSON字段
  const answers = parseJsonField(testResult.answers)
  const result = parseJsonField(testResult.result)
  
  // 格式化响应
  const formattedResult = {
    id: testResult.id,
    testType: testResult.testType,
    result,
    shareToken: testResult.shareToken,
    createdAt: testResult.createdAt,
    user: testResult.user ? {
      username: testResult.user.username,
      avatar: testResult.user.avatar
    } : null,
    // 不返回原始答案以保护隐私
    hasAnswers: !!answers
  }
  
  return successResponse(
    formattedResult,
    'Test result retrieved successfully'
  )
})

// PUT /api/tests/result/[token] - 更新测试结果的公开状态
export const PUT = withErrorHandling(async (
  request: NextRequest,
  { params }: RouteParams
) => {
  const { token } = params
  
  if (!token) {
    return ApiErrors.validation('Share token is required')
  }
  
  try {
    const body = await request.json()
    const { isPublic, userId } = body
    
    // 查找测试结果
    const testResult = await prisma.testResult.findUnique({
      where: { shareToken: token }
    })
    
    if (!testResult) {
      return ApiErrors.notFound('Test result not found')
    }
    
    // 验证权限（只有结果的所有者可以修改）
    if (testResult.userId !== userId) {
      return ApiErrors.forbidden('You can only modify your own test results')
    }
    
    // 更新公开状态
    const updatedResult = await prisma.testResult.update({
      where: { shareToken: token },
      data: { isPublic: !!isPublic }
    })
    
    return successResponse(
      {
        shareToken: updatedResult.shareToken,
        isPublic: updatedResult.isPublic
      },
      'Test result visibility updated successfully'
    )
    
  } catch (error) {
    if (error instanceof SyntaxError) {
      return ApiErrors.invalidJson('Invalid JSON in request body')
    }
    throw error
  }
})

// DELETE /api/tests/result/[token] - 删除测试结果
export const DELETE = withErrorHandling(async (
  request: NextRequest,
  { params }: RouteParams
) => {
  const { token } = params
  
  if (!token) {
    return ApiErrors.validation('Share token is required')
  }
  
  // 从请求头或查询参数获取用户ID（实际应用中应该从认证中获取）
  const { searchParams } = new URL(request.url)
  const userId = searchParams.get('userId')
  
  // 查找测试结果
  const testResult = await prisma.testResult.findUnique({
    where: { shareToken: token }
  })
  
  if (!testResult) {
    return ApiErrors.notFound('Test result not found')
  }
  
  // 验证权限（只有结果的所有者可以删除）
  if (testResult.userId !== userId) {
    return ApiErrors.forbidden('You can only delete your own test results')
  }
  
  // 删除测试结果
  await prisma.testResult.delete({
    where: { shareToken: token }
  })
  
  return successResponse(
    { 
      shareToken: token,
      deleted: true 
    },
    'Test result deleted successfully'
  )
})
