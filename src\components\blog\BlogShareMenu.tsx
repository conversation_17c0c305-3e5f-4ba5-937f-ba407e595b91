'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Share2, Twitter, Facebook, Linkedin, Link, MessageCircle } from 'lucide-react';
import { cn } from '@/lib/utils';
import { BlogPost } from '@/types';

interface BlogShareMenuProps {
  post: BlogPost;
  className?: string;
}

export function BlogShareMenu({ post, className }: BlogShareMenuProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [copied, setCopied] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const shareUrl = typeof window !== 'undefined' ? window.location.href : '';
  const shareText = `${post.title} - ${post.excerpt}`;

  const shareOptions = [
    {
      name: 'Twitter',
      icon: Twitter,
      url: `https://twitter.com/intent/tweet?text=${encodeURIComponent(shareText)}&url=${encodeURIComponent(shareUrl)}`,
      color: 'hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-900/20'
    },
    {
      name: 'Facebook',
      icon: Facebook,
      url: `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}`,
      color: 'hover:bg-blue-50 hover:text-blue-700 dark:hover:bg-blue-900/20'
    },
    {
      name: 'LinkedIn',
      icon: Linkedin,
      url: `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(shareUrl)}`,
      color: 'hover:bg-blue-50 hover:text-blue-800 dark:hover:bg-blue-900/20'
    },
    {
      name: 'WeChat',
      icon: MessageCircle,
      url: `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(shareUrl)}`,
      color: 'hover:bg-green-50 hover:text-green-600 dark:hover:bg-green-900/20',
      isQR: true
    }
  ];

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(shareUrl);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy link:', error);
    }
  };

  const handleShare = (option: typeof shareOptions[0]) => {
    if (option.isQR) {
      // 显示二维码模态框
      // TODO: 实现二维码分享功能
      return;
    }
    
    window.open(option.url, '_blank', 'width=600,height=400');
    setIsOpen(false);
  };

  const handleNativeShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: post.title,
          text: post.excerpt,
          url: shareUrl,
        });
        setIsOpen(false);
      } catch (error) {
        console.error('Native share failed:', error);
      }
    }
  };

  return (
    <div className={cn('relative', className)} ref={menuRef}>
      {/* 触发按钮 */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={cn(
          'w-12 h-12 rounded-full border transition-all duration-300',
          'bg-white dark:bg-dark-800 border-mystical-200 dark:border-dark-600',
          'text-mystical-600 dark:text-mystical-400',
          'hover:bg-mystical-50 dark:hover:bg-dark-700 hover:border-mystical-300 dark:hover:border-dark-500',
          'hover:scale-105 hover:shadow-mystical-lg',
          'flex items-center justify-center group',
          isOpen && 'bg-mystical-50 dark:bg-dark-700 border-mystical-300 dark:border-dark-500'
        )}
        title="分享文章"
      >
        <Share2 className="w-5 h-5 group-hover:scale-110 transition-transform duration-300" />
      </button>

      {/* 分享菜单 */}
      {isOpen && (
        <div className={cn(
          'absolute bottom-full left-0 mb-2 min-w-[200px]',
          'bg-white dark:bg-dark-800 rounded-lg shadow-mystical-lg',
          'border border-mystical-200 dark:border-dark-600',
          'py-2 z-50',
          'animate-in slide-in-from-bottom-2 duration-200'
        )}>
          {/* 原生分享（如果支持） */}
          {typeof navigator !== 'undefined' && navigator.share && (
            <>
              <button
                onClick={handleNativeShare}
                className={cn(
                  'w-full flex items-center gap-3 px-4 py-3 text-sm',
                  'text-mystical-700 dark:text-mystical-300',
                  'hover:bg-mystical-50 dark:hover:bg-dark-700 transition-colors'
                )}
              >
                <Share2 className="w-4 h-4" />
                <span>系统分享</span>
              </button>
              <div className="border-t border-mystical-200 dark:border-dark-600 my-1" />
            </>
          )}

          {/* 社交媒体分享选项 */}
          {shareOptions.map((option) => (
            <button
              key={option.name}
              onClick={() => handleShare(option)}
              className={cn(
                'w-full flex items-center gap-3 px-4 py-3 text-sm',
                'text-mystical-700 dark:text-mystical-300',
                'transition-colors',
                option.color
              )}
            >
              <option.icon className="w-4 h-4" />
              <span>{option.name}</span>
            </button>
          ))}

          <div className="border-t border-mystical-200 dark:border-dark-600 my-1" />

          {/* 复制链接 */}
          <button
            onClick={handleCopyLink}
            className={cn(
              'w-full flex items-center gap-3 px-4 py-3 text-sm',
              'text-mystical-700 dark:text-mystical-300',
              'hover:bg-mystical-50 dark:hover:bg-dark-700 transition-colors'
            )}
          >
            <Link className="w-4 h-4" />
            <span>{copied ? '已复制!' : '复制链接'}</span>
          </button>
        </div>
      )}
    </div>
  );
}
