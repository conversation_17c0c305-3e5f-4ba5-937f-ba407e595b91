'use client';

import React, { useState } from 'react';
import { Heart, Share2, Bookmark, MessageCircle } from 'lucide-react';
import { cn } from '@/lib/utils';
import { BlogPost } from '@/types';

interface BlogEngagementButtonsProps {
  post: BlogPost;
  className?: string;
}

export function BlogEngagementButtons({ post, className }: BlogEngagementButtonsProps) {
  const [isLiked, setIsLiked] = useState(false);
  const [isBookmarked, setIsBookmarked] = useState(false);
  const [likeCount, setLikeCount] = useState(post.likeCount);

  const handleLike = async () => {
    try {
      const newLiked = !isLiked;
      setIsLiked(newLiked);
      setLikeCount(prev => newLiked ? prev + 1 : prev - 1);
      
      // TODO: 实际的API调用
      // await toggleLike(post.id);
    } catch (error) {
      // 回滚状态
      setIsLiked(!isLiked);
      setLikeCount(prev => isLiked ? prev + 1 : prev - 1);
    }
  };

  const handleBookmark = async () => {
    try {
      setIsBookmarked(!isBookmarked);
      // TODO: 实际的API调用
      // await toggleBookmark(post.id);
    } catch (error) {
      setIsBookmarked(!isBookmarked);
    }
  };

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: post.title,
        text: post.excerpt,
        url: window.location.href,
      });
    } else {
      // 回退到复制链接
      navigator.clipboard.writeText(window.location.href);
      // TODO: 显示提示消息
    }
  };

  const scrollToComments = () => {
    const commentsElement = document.getElementById('comments');
    if (commentsElement) {
      commentsElement.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <div className={cn(
      'fixed left-6 bottom-6 flex flex-col gap-3 z-40',
      className
    )}>
      {/* 点赞按钮 */}
      <div className="flex flex-col items-center">
        <button
          onClick={handleLike}
          className={cn(
            'w-12 h-12 rounded-full border transition-all duration-300',
            'flex items-center justify-center group',
            'hover:scale-105 hover:shadow-mystical-lg',
            isLiked
              ? 'bg-red-500 border-red-500 text-white'
              : 'bg-white dark:bg-dark-800 border-mystical-200 dark:border-dark-600 text-mystical-600 dark:text-mystical-400 hover:bg-red-50 dark:hover:bg-red-900/20 hover:border-red-300'
          )}
          title={isLiked ? '取消点赞' : '点赞文章'}
        >
          <Heart 
            className={cn(
              'w-5 h-5 transition-all duration-300',
              isLiked ? 'fill-current' : 'group-hover:scale-110'
            )} 
          />
        </button>
        {likeCount > 0 && (
          <span className="text-xs text-mystical-500 dark:text-mystical-400 mt-1 font-medium">
            {likeCount}
          </span>
        )}
      </div>

      {/* 评论按钮 */}
      <div className="flex flex-col items-center">
        <button
          onClick={scrollToComments}
          className={cn(
            'w-12 h-12 rounded-full border transition-all duration-300',
            'bg-white dark:bg-dark-800 border-mystical-200 dark:border-dark-600',
            'text-mystical-600 dark:text-mystical-400',
            'hover:bg-mystical-50 dark:hover:bg-dark-700 hover:border-mystical-300 dark:hover:border-dark-500',
            'hover:scale-105 hover:shadow-mystical-lg',
            'flex items-center justify-center group'
          )}
          title="查看评论"
        >
          <MessageCircle className="w-5 h-5 group-hover:scale-110 transition-transform duration-300" />
        </button>
        {post.comments && post.comments.length > 0 && (
          <span className="text-xs text-mystical-500 dark:text-mystical-400 mt-1 font-medium">
            {post.comments.length}
          </span>
        )}
      </div>

      {/* 收藏按钮 */}
      <button
        onClick={handleBookmark}
        className={cn(
          'w-12 h-12 rounded-full border transition-all duration-300',
          'flex items-center justify-center group',
          'hover:scale-105 hover:shadow-mystical-lg',
          isBookmarked
            ? 'bg-gold-500 border-gold-500 text-white'
            : 'bg-white dark:bg-dark-800 border-mystical-200 dark:border-dark-600 text-mystical-600 dark:text-mystical-400 hover:bg-gold-50 dark:hover:bg-gold-900/20 hover:border-gold-300'
        )}
        title={isBookmarked ? '取消收藏' : '收藏文章'}
      >
        <Bookmark 
          className={cn(
            'w-5 h-5 transition-all duration-300',
            isBookmarked ? 'fill-current' : 'group-hover:scale-110'
          )} 
        />
      </button>

      {/* 分享按钮 */}
      <button
        onClick={handleShare}
        className={cn(
          'w-12 h-12 rounded-full border transition-all duration-300',
          'bg-white dark:bg-dark-800 border-mystical-200 dark:border-dark-600',
          'text-mystical-600 dark:text-mystical-400',
          'hover:bg-mystical-50 dark:hover:bg-dark-700 hover:border-mystical-300 dark:hover:border-dark-500',
          'hover:scale-105 hover:shadow-mystical-lg',
          'flex items-center justify-center group'
        )}
        title="分享文章"
      >
        <Share2 className="w-5 h-5 group-hover:scale-110 transition-transform duration-300" />
      </button>
    </div>
  );
}
