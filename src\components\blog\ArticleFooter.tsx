'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Calendar, Clock, Eye, Heart, Share2, Tag, User } from 'lucide-react';
import { cn } from '@/lib/utils';
import { BlogPost } from '@/types';
import { TestRecommendation, RelatedPostsRecommendation } from './TestRecommendation';
import { BlogComments } from './BlogComments';
import { BlogShareMenu } from './BlogShareMenu';

interface ArticleFooterProps {
  post: BlogPost;
  relatedPosts?: BlogPost[];
  className?: string;
}

export function ArticleFooter({ post, relatedPosts = [], className }: ArticleFooterProps) {
  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    }).format(date);
  };

  return (
    <footer className={cn('space-y-12', className)}>
      {/* 文章标签 */}
      {post.tags && JSON.parse(post.tags).length > 0 && (
        <div className="flex flex-wrap gap-2">
          {JSON.parse(post.tags).map((tag: string) => (
            <Link
              key={tag}
              href={`/blog?tag=${encodeURIComponent(tag)}`}
              className={cn(
                'inline-flex items-center gap-1 px-3 py-1 text-sm font-medium rounded-full',
                'bg-mystical-100 dark:bg-dark-700 text-mystical-600 dark:text-mystical-400',
                'hover:bg-mystical-200 dark:hover:bg-dark-600 hover:text-mystical-700 dark:hover:text-mystical-300',
                'transition-colors duration-200'
              )}
            >
              <Tag className="w-3 h-3" />
              <span>{tag}</span>
            </Link>
          ))}
        </div>
      )}

      {/* 文章统计和分享 */}
      <div className="flex items-center justify-between py-6 border-y border-mystical-200 dark:border-dark-700">
        <div className="flex items-center gap-6 text-sm text-mystical-500 dark:text-mystical-400">
          <div className="flex items-center gap-1">
            <Eye className="w-4 h-4" />
            <span>{post.viewCount.toLocaleString()} 次阅读</span>
          </div>
          <div className="flex items-center gap-1">
            <Heart className="w-4 h-4" />
            <span>{post.likeCount} 点赞</span>
          </div>
          <div className="flex items-center gap-1">
            <Share2 className="w-4 h-4" />
            <span>{post.shareCount} 分享</span>
          </div>
        </div>

        <div className="flex items-center gap-3">
          <BlogShareMenu post={post} />
        </div>
      </div>

      {/* 作者信息卡片 */}
      <div className="bg-mystical-50 dark:bg-dark-800 rounded-xl p-6 border border-mystical-200 dark:border-dark-600">
        <div className="flex items-start gap-4">
          {/* 作者头像 */}
          <div className="flex-shrink-0">
            {post.author?.avatar ? (
              <Image
                src={post.author.avatar}
                alt={post.author.name}
                width={64}
                height={64}
                className="rounded-full border-2 border-mystical-200 dark:border-dark-600"
              />
            ) : (
              <div className="w-16 h-16 bg-mystical-200 dark:bg-dark-600 rounded-full flex items-center justify-center">
                <User className="w-8 h-8 text-mystical-500 dark:text-mystical-400" />
              </div>
            )}
          </div>

          {/* 作者信息 */}
          <div className="flex-1">
            <h4 className="text-lg font-bold text-mystical-900 dark:text-white mb-2">
              {post.author?.name || '神秘作者'}
            </h4>
            <p className="text-mystical-600 dark:text-mystical-300 mb-4 leading-relaxed">
              {post.author?.bio || '专业的玄学研究者，致力于分享古老智慧与现代生活的结合。'}
            </p>
            
            {/* 作者统计 */}
            <div className="flex items-center gap-4 text-sm text-mystical-500 dark:text-mystical-400">
              <span>已发布 {post.author?.postCount || 0} 篇文章</span>
              <span>•</span>
              <span>专业玄学导师</span>
            </div>

            {/* 社交链接 */}
            {post.author?.socialLinks && (
              <div className="flex items-center gap-3 mt-4">
                {post.author.socialLinks.website && (
                  <Link
                    href={post.author.socialLinks.website}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-mystical-500 hover:text-mystical-600 dark:text-mystical-400 dark:hover:text-mystical-300 transition-colors"
                  >
                    个人网站
                  </Link>
                )}
                {post.author.socialLinks.twitter && (
                  <Link
                    href={post.author.socialLinks.twitter}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-mystical-500 hover:text-mystical-600 dark:text-mystical-400 dark:hover:text-mystical-300 transition-colors"
                  >
                    Twitter
                  </Link>
                )}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 测试推荐 */}
      <TestRecommendation post={post} />

      {/* 相关文章推荐 */}
      {relatedPosts.length > 0 && (
        <RelatedPostsRecommendation 
          currentPost={post} 
          relatedPosts={relatedPosts} 
        />
      )}

      {/* 评论系统 */}
      <BlogComments post={post} />

      {/* 底部导航 */}
      <div className="flex items-center justify-between pt-8 border-t border-mystical-200 dark:border-dark-700">
        <Link
          href="/blog"
          className={cn(
            'inline-flex items-center gap-2 px-4 py-2 text-sm font-medium',
            'text-mystical-600 dark:text-mystical-400',
            'hover:text-mystical-700 dark:hover:text-mystical-300',
            'transition-colors duration-200'
          )}
        >
          ← 返回博客列表
        </Link>

        <div className="flex items-center gap-4">
          <Link
            href="/tarot/test"
            className={cn(
              'inline-flex items-center gap-2 px-4 py-2 text-sm font-medium rounded-lg',
              'bg-mystical-500 hover:bg-mystical-600 text-white',
              'transition-colors duration-200'
            )}
          >
            🔮 免费塔罗测试
          </Link>
          <Link
            href="/astrology/test"
            className={cn(
              'inline-flex items-center gap-2 px-4 py-2 text-sm font-medium rounded-lg',
              'bg-gold-500 hover:bg-gold-600 text-white',
              'transition-colors duration-200'
            )}
          >
            ⭐ 星座性格测试
          </Link>
        </div>
      </div>

      {/* 文章元信息 */}
      <div className="text-xs text-mystical-400 dark:text-mystical-500 pt-4 border-t border-mystical-100 dark:border-dark-800">
        <div className="flex flex-wrap items-center gap-4">
          <span>发布时间：{formatDate(post.publishedAt || post.createdAt)}</span>
          {post.updatedAt && post.updatedAt !== post.createdAt && (
            <span>更新时间：{formatDate(post.updatedAt)}</span>
          )}
          <span>阅读时长：约 {post.readingTime} 分钟</span>
          <span>文章分类：{post.category}</span>
        </div>
        
        {/* AI生成标识 */}
        {post.aiMetadata && (
          <div className="mt-2 text-mystical-400 dark:text-mystical-500">
            <span>本文由AI辅助创作，经专业编辑审核</span>
          </div>
        )}
      </div>
    </footer>
  );
}
