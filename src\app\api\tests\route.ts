// 测试API路由 - 开始测试和提交答案
// Test API Routes - Start test and submit answers

import { NextRequest } from 'next/server'
import { prisma, TestType } from '@/lib/prisma'
import {
  successResponse,
  ApiErrors,
  withErrorHandling,
  parseJsonField,
  stringifyJsonField
} from '@/lib/api-response'
import { randomBytes } from 'crypto'

// POST /api/tests - 开始新测试或提交测试答案
export const POST = withErrorHandling(async (request: NextRequest) => {
  try {
    const body = await request.json()
    const { action, testType, userId, answers, sessionId } = body
    
    if (action === 'start') {
      // 开始新测试
      return await startNewTest(testType, userId)
    } else if (action === 'submit') {
      // 提交测试答案
      return await submitTestAnswers(sessionId, answers, userId)
    } else {
      return ApiErrors.validation('Invalid action. Use "start" or "submit"')
    }
    
  } catch (error) {
    if (error instanceof SyntaxError) {
      return ApiErrors.invalidJson('Invalid JSON in request body')
    }
    throw error
  }
})

// 开始新测试
async function startNewTest(testType: string, userId?: string) {
  // 验证测试类型
  if (!Object.values(TestType).includes(testType as any)) {
    return ApiErrors.validation(`Invalid test type: ${testType}`)
  }
  
  // 生成会话ID（用于跟踪测试进度）
  const sessionId = randomBytes(16).toString('hex')
  
  // 根据测试类型返回相应的问题
  const questions = getTestQuestions(testType)
  
  return successResponse(
    {
      sessionId,
      testType,
      questions,
      totalQuestions: questions.length,
      userId: userId || null
    },
    'Test session started successfully'
  )
}

// 提交测试答案
async function submitTestAnswers(sessionId: string, answers: any, userId?: string) {
  if (!sessionId || !answers) {
    return ApiErrors.validation('Session ID and answers are required')
  }
  
  // 这里应该根据答案生成测试结果
  // 为了演示，我们创建一个简单的结果
  const result = generateTestResult(answers)
  
  // 生成分享令牌
  const shareToken = randomBytes(12).toString('hex')
  
  // 保存测试结果到数据库
  const testResult = await prisma.testResult.create({
    data: {
      userId: userId || null,
      testType: result.testType,
      answers: stringifyJsonField(answers),
      result: stringifyJsonField(result),
      shareToken,
      isPublic: true
    }
  })
  
  return successResponse(
    {
      resultId: testResult.id,
      shareToken,
      result: {
        ...result,
        shareUrl: `/tests/result/${shareToken}`
      }
    },
    'Test completed successfully',
    undefined,
    201
  )
}

// 获取测试问题（示例实现）
function getTestQuestions(testType: string) {
  const questionSets: Record<string, any[]> = {
    [TestType.TAROT]: [
      {
        id: 1,
        question: "What area of your life would you like guidance on?",
        type: "single_choice",
        options: [
          { id: "love", text: "Love & Relationships" },
          { id: "career", text: "Career & Work" },
          { id: "personal", text: "Personal Growth" },
          { id: "general", text: "General Life Guidance" }
        ]
      },
      {
        id: 2,
        question: "How do you feel about taking risks?",
        type: "single_choice",
        options: [
          { id: "adventurous", text: "I embrace new adventures" },
          { id: "cautious", text: "I prefer to be cautious" },
          { id: "balanced", text: "I balance risk and safety" },
          { id: "avoidant", text: "I avoid risks when possible" }
        ]
      },
      {
        id: 3,
        question: "What draws you to tarot reading?",
        type: "multiple_choice",
        options: [
          { id: "guidance", text: "Seeking guidance" },
          { id: "curiosity", text: "Pure curiosity" },
          { id: "spiritual", text: "Spiritual connection" },
          { id: "entertainment", text: "Entertainment" }
        ]
      }
    ],
    [TestType.ASTROLOGY]: [
      {
        id: 1,
        question: "What's your birth date?",
        type: "date",
        placeholder: "YYYY-MM-DD"
      },
      {
        id: 2,
        question: "What time were you born? (if known)",
        type: "time",
        placeholder: "HH:MM",
        optional: true
      },
      {
        id: 3,
        question: "What's your birth location?",
        type: "text",
        placeholder: "City, Country"
      }
    ],
    [TestType.NUMEROLOGY]: [
      {
        id: 1,
        question: "What's your full birth name?",
        type: "text",
        placeholder: "First Middle Last"
      },
      {
        id: 2,
        question: "What's your birth date?",
        type: "date",
        placeholder: "YYYY-MM-DD"
      },
      {
        id: 3,
        question: "What aspect interests you most?",
        type: "single_choice",
        options: [
          { id: "life_path", text: "Life Path Number" },
          { id: "destiny", text: "Destiny Number" },
          { id: "soul", text: "Soul Urge Number" },
          { id: "personality", text: "Personality Number" }
        ]
      }
    ]
  }
  
  return questionSets[testType] || []
}

// 生成测试结果（示例实现）
function generateTestResult(answers: any) {
  // 这里应该包含实际的算法来生成结果
  // 为了演示，我们返回一个简单的结果
  
  const testType = answers.testType || TestType.TAROT
  
  const results: Record<string, any> = {
    [TestType.TAROT]: {
      testType,
      cards: ["The Fool", "The Magician", "The High Priestess"],
      interpretation: "A new beginning awaits you. Trust your intuition and embrace the journey ahead.",
      themes: ["new beginnings", "intuition", "spiritual growth"],
      advice: "Stay open to new opportunities and trust your inner wisdom."
    },
    [TestType.ASTROLOGY]: {
      testType,
      sunSign: "Virgo",
      moonSign: "Pisces", 
      risingSign: "Gemini",
      interpretation: "You have a practical nature with deep emotional intuition and excellent communication skills.",
      traits: ["analytical", "intuitive", "communicative"],
      compatibility: ["Taurus", "Cancer", "Scorpio"]
    },
    [TestType.NUMEROLOGY]: {
      testType,
      lifePathNumber: 7,
      destinyNumber: 3,
      interpretation: "You are a natural seeker of truth with creative expression abilities.",
      strengths: ["analytical", "spiritual", "creative"],
      challenges: ["overthinking", "isolation", "perfectionism"]
    }
  }
  
  return results[testType] || {
    testType,
    interpretation: "Thank you for taking the test. Your results are being processed.",
    message: "Please try again later for detailed results."
  }
}
