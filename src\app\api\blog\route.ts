// 博客API路由 - 获取文章列表和创建文章
// Blog API Routes - Get posts list and create posts

import { NextRequest } from 'next/server'
import { prisma, PostStatus } from '@/lib/prisma'
import {
  successResponse,
  paginatedResponse,
  ApiErrors,
  withErrorHandling,
  parseJsonField,
  stringifyJsonField
} from '@/lib/api-response'

// GET /api/blog - 获取博客文章列表
export const GET = withErrorHandling(async (request: NextRequest) => {
  const { searchParams } = new URL(request.url)
  
  // 解析查询参数
  const page = parseInt(searchParams.get('page') || '1')
  const limit = Math.min(parseInt(searchParams.get('limit') || '10'), 50) // 最大50条
  const category = searchParams.get('category')
  const locale = searchParams.get('locale') || 'en'
  const search = searchParams.get('search')
  const status = searchParams.get('status') || PostStatus.PUBLISHED
  const tags = searchParams.get('tags')?.split(',').filter(Boolean)
  
  // 构建查询条件
  const where: any = {
    locale,
    status
  }
  
  if (category) {
    where.category = category
  }
  
  if (search) {
    where.OR = [
      { title: { contains: search } },
      { content: { contains: search } },
      { excerpt: { contains: search } }
    ]
  }
  
  // 标签搜索（使用JSON查询）
  if (tags && tags.length > 0) {
    where.AND = tags.map(tag => ({
      tags: { contains: `"${tag}"` }
    }))
  }
  
  // 计算偏移量
  const skip = (page - 1) * limit
  
  // 查询文章和总数
  const [posts, total] = await Promise.all([
    prisma.blogPost.findMany({
      where,
      select: {
        id: true,
        title: true,
        slug: true,
        excerpt: true,
        coverImage: true,
        locale: true,
        category: true,
        tags: true,
        status: true,
        publishedAt: true,
        viewCount: true,
        readingTime: true,
        seoTitle: true,
        seoDescription: true,
        keywords: true,
        metadata: true,
        createdAt: true,
        updatedAt: true
      },
      orderBy: [
        { publishedAt: 'desc' },
        { createdAt: 'desc' }
      ],
      skip,
      take: limit
    }),
    prisma.blogPost.count({ where })
  ])
  
  // 解析JSON字段
  const formattedPosts = posts.map(post => ({
    ...post,
    tags: parseJsonField<string[]>(post.tags) || [],
    keywords: parseJsonField<string[]>(post.keywords) || [],
    metadata: parseJsonField(post.metadata) || {}
  }))
  
  return paginatedResponse(
    formattedPosts,
    page,
    limit,
    total,
    `Found ${total} posts`
  )
})

// POST /api/blog - 创建新文章
export const POST = withErrorHandling(async (request: NextRequest) => {
  try {
    const body = await request.json()
    
    // 验证必需字段
    const { title, content, locale, category } = body
    if (!title || !content || !locale || !category) {
      return ApiErrors.validation('Missing required fields: title, content, locale, category')
    }
    
    // 生成slug（如果没有提供）
    const slug = body.slug || title
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim()
    
    // 检查slug是否已存在
    const existingPost = await prisma.blogPost.findUnique({
      where: { slug }
    })
    
    if (existingPost) {
      return ApiErrors.validation(`Slug "${slug}" already exists`)
    }
    
    // 处理JSON字段
    const tags = Array.isArray(body.tags) ? stringifyJsonField(body.tags) : '[]'
    const keywords = Array.isArray(body.keywords) ? stringifyJsonField(body.keywords) : '[]'
    const metadata = body.metadata ? stringifyJsonField(body.metadata) : null
    
    // 计算阅读时间（简单估算：250字/分钟）
    const readingTime = Math.ceil(content.length / 250 / 4) // 假设平均4个字符为一个单词
    
    // 创建文章
    const post = await prisma.blogPost.create({
      data: {
        title,
        slug,
        content,
        excerpt: body.excerpt || content.substring(0, 200) + '...',
        coverImage: body.coverImage,
        locale,
        category,
        tags,
        status: body.status || PostStatus.DRAFT,
        publishedAt: body.status === PostStatus.PUBLISHED ? new Date() : null,
        readingTime,
        seoTitle: body.seoTitle,
        seoDescription: body.seoDescription,
        keywords,
        metadata
      }
    })
    
    // 格式化响应
    const formattedPost = {
      ...post,
      tags: parseJsonField<string[]>(post.tags) || [],
      keywords: parseJsonField<string[]>(post.keywords) || [],
      metadata: parseJsonField(post.metadata) || {}
    }
    
    return successResponse(
      formattedPost,
      'Post created successfully',
      undefined,
      201
    )
    
  } catch (error) {
    if (error instanceof SyntaxError) {
      return ApiErrors.invalidJson('Invalid JSON in request body')
    }
    throw error
  }
})
