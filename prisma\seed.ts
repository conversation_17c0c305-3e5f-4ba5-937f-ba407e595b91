import { PrismaClient } from '@prisma/client'
import { sampleBlogPosts, sampleAuthors, sampleCategories } from '../src/data/sample-blog-posts'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 开始播种数据库... / Starting database seeding...')

  // 清理现有数据
  await prisma.blogView.deleteMany()
  await prisma.comment.deleteMany()
  await prisma.userFavorite.deleteMany()
  await prisma.blogPost.deleteMany()
  await prisma.testResult.deleteMany()
  await prisma.user.deleteMany()

  console.log('✅ 清理完成 / Cleanup completed')

  // 创建示例作者用户
  const authors = []
  for (const authorData of sampleAuthors) {
    const author = await prisma.user.create({
      data: {
        email: authorData.email,
        username: authorData.name.toLowerCase().replace(/\s+/g, '-'),
        locale: 'zh-C<PERSON>',
        theme: 'light'
      },
    })
    authors.push(author)
  }

  // 创建测试用户
  const testUser = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      username: 'testuser',
      locale: 'zh-CN',
      theme: 'light'
    },
  })

  console.log('✅ 用户创建完成 / Users created')

  // 创建示例博客文章
  // Create sample blog posts
  for (let i = 0; i < sampleBlogPosts.length; i++) {
    const postData = sampleBlogPosts[i]
    const author = authors[i % authors.length] // 轮流分配作者

    await prisma.blogPost.create({
      data: {
        title: postData.title!,
        slug: postData.slug!,
        content: postData.content!,
        excerpt: postData.excerpt!,
        locale: postData.locale!,
        category: postData.category!,
        tags: postData.tags!,
        status: postData.status || 'PUBLISHED',
        publishedAt: new Date(),
        readingTime: postData.readingTime || 5,
        featured: postData.featured || false,
        viewCount: Math.floor(Math.random() * 1000) + 100,
        likeCount: Math.floor(Math.random() * 50) + 10,
        shareCount: Math.floor(Math.random() * 20) + 5,
        seoTitle: postData.seoTitle,
        seoDescription: postData.seoDescription,
        keywords: postData.keywords!,
        coverImage: `/images/blog/cover-${i + 1}.jpg`,
        metadata: JSON.stringify({
          author: {
            id: author.id,
            name: sampleAuthors[i % sampleAuthors.length].name,
            avatar: sampleAuthors[i % sampleAuthors.length].avatar
          },
          category: sampleCategories.find(cat => cat.slug === postData.category),
          generatedBy: 'seed',
          createdAt: new Date().toISOString()
        })
      },
    })
  }

  console.log('✅ 博客文章创建完成 / Blog posts created')

  // 创建示例测试结果
  // Create sample test results
  const testResults = [
    {
      userId: testUser.id,
      testType: 'TAROT',
      answers: JSON.stringify({
        question1: 'love',
        question2: 'future',
        cards: ['the-fool', 'the-magician', 'the-high-priestess']
      }),
      result: JSON.stringify({
        interpretation: '你正处于人生的新开始阶段，充满无限可能。',
        cards: [
          { name: '愚者', meaning: '新的开始，冒险精神' },
          { name: '魔术师', meaning: '创造力，意志力' },
          { name: '女祭司', meaning: '直觉，内在智慧' }
        ],
        advice: '相信你的直觉，勇敢地迈出第一步。'
      }),
      shareToken: 'tarot-result-123',
      isPublic: true
    },
    {
      userId: authors[0].id,
      testType: 'ASTROLOGY',
      answers: JSON.stringify({
        birthDate: '1990-05-15',
        birthTime: '14:30',
        birthPlace: 'Beijing, China'
      }),
      result: JSON.stringify({
        sunSign: 'Taurus',
        moonSign: 'Cancer',
        risingSign: 'Virgo',
        personality: 'You are practical, emotional, and detail-oriented.',
        strengths: ['Reliable', 'Caring', 'Analytical'],
        challenges: ['Stubborn', 'Overly critical', 'Worry-prone']
      }),
      shareToken: 'astro-result-456',
      isPublic: false
    }
  ]

  for (const result of testResults) {
    await prisma.testResult.create({
      data: result,
    })
  }

  console.log('✅ 测试结果创建完成 / Test results created')

  // 输出统计信息
  const stats = {
    users: await prisma.user.count(),
    posts: await prisma.blogPost.count(),
    testResults: await prisma.testResult.count()
  }

  console.log('📊 数据统计 / Statistics:')
  console.log(`   用户 / Users: ${stats.users}`)
  console.log(`   文章 / Posts: ${stats.posts}`)
  console.log(`   测试结果 / Test Results: ${stats.testResults}`)

  console.log('🎉 数据库播种完成！/ Database seeding completed!')
}

main()
  .then(async () => {
    await prisma.$disconnect()
  })
  .catch(async (e) => {
    console.error('❌ 播种过程中出错 / Error during seeding:', e)
    await prisma.$disconnect()
    process.exit(1)
  })
