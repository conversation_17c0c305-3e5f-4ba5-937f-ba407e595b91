---
type: "always_apply"
description: "博文管理系统和内容导入规范"
---
# 博文管理系统规范

> **注意**：本规范采用纯数据库存储策略，简化内容管理流程，专注于内容质量和SEO优化。

## 博文生命周期管理
```typescript
// 博文状态枚举
enum PostStatus {
  DRAFT = 'draft',           // 草稿
  PENDING = 'pending',       // 待审核
  SCHEDULED = 'scheduled',   // 定时发布
  PUBLISHED = 'published',   // 已发布
  ARCHIVED = 'archived',     // 已归档
  DELETED = 'deleted'        // 已删除（软删除）
}

// 博文工作流
const POST_WORKFLOW = {
  // 创建流程
  creation: {
    import: 'AI生成 -> 导入 -> 解析 -> 预览',
    manual: '手动创建 -> 编辑 -> 预览',
    bulk: '批量导入 -> 解析 -> 批量预览'
  },
  
  // 审核流程
  review: {
    content: 'AI内容检查 -> 人工审核 -> SEO优化',
    seo: 'SEO分析 -> 关键词优化 -> 元数据完善',
    quality: '内容质量检查 -> 图片优化 -> 链接检查'
  },
  
  // 发布流程
  publishing: {
    immediate: '立即发布 -> 索引提交 -> 社交分享',
    scheduled: '定时发布 -> 自动发布 -> 通知推送',
    batch: '批量发布 -> 分批处理 -> 进度跟踪'
  }
};
```

## AI内容直接入库系统
```typescript
// AI生成内容直接存储到数据库
interface AIGeneratedPost {
  // 基础信息
  title: string;
  slug: string;
  content: string; // 富文本内容，支持HTML
  excerpt: string;
  locale: string;
  category: string;
  tags: string[];

  // SEO信息
  seoTitle?: string;
  seoDescription?: string;
  keywords: string[];

  // 媒体信息
  coverImage?: string;
  images?: {
    url: string;
    alt: string;
    caption?: string;
  }[];

  // 发布信息
  status: PostStatus;
  publishedAt?: Date;

  // AI元数据
  aiMetadata: {
    model: string;
    generatedAt: Date;
    prompt?: string;
    confidence?: number;
  };
}

// 数据库直接存储配置
const DATABASE_STORAGE_CONFIG = {
  // 内容处理设置
  contentProcessing: {
    maxContentLength: '50MB', // 数据库TEXT字段限制
    allowedHtmlTags: ['p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'strong', 'em', 'ul', 'ol', 'li', 'a', 'img', 'blockquote', 'code', 'pre'],
    imageOptimization: true,
    autoSlugGeneration: true
  },

  // 批量创建设置
  batchCreation: {
    maxBatchSize: 50,
    concurrency: 3,
    timeout: 60000,
    transactionMode: true // 确保数据一致性
  },

  // 自动处理选项
  autoProcessing: {
    generateSlug: true,
    extractKeywords: true,
    seoAnalysis: true,
    duplicateCheck: true,
    readingTimeCalculation: true,
    autoExcerpt: true
  }
};
```

## 数据库存储结构
```typescript
// 博文数据库存储规范
const BLOG_DATABASE_STRUCTURE = {
  // 主表结构（基于Prisma Schema）
  blogPostTable: {
    primaryKey: 'id (cuid)',
    uniqueFields: ['slug'],
    indexedFields: ['locale', 'category', 'status', 'publishedAt', 'slug'],
    fullTextSearch: ['title', 'content', 'excerpt'],

    // 多语言支持
    localeHandling: {
      strategy: 'single table with locale field',
      supportedLocales: ['en', 'zh-CN', 'zh-TW', 'es', 'pt', 'hi', 'ja'],
      slugPattern: '{slug}-{locale}' // 例: tarot-guide-en, tarot-guide-zh
    },

    // 分类管理
    categoryStructure: {
      predefinedCategories: ['tarot', 'astrology', 'numerology', 'crystal', 'palmistry', 'dreams'],
      hierarchical: false, // 扁平结构，简化管理
      tagsSupport: true // 使用tags字段支持多标签
    }
  },

  // 图片资源管理
  imageStorage: {
    strategy: 'CDN + Database URLs',
    storageLocation: 'public/images/blog/{year}/{month}/',
    databaseField: 'coverImage, images JSON array',
    optimization: 'Sharp处理，多尺寸生成',
    naming: '{timestamp}-{slug}-{index}.{ext}'
  },

  // 内容版本控制
  versionControl: {
    strategy: 'updatedAt timestamp',
    backup: 'soft delete with status=DELETED',
    history: 'optional separate audit table'
  }
};
```

## AI内容直接入库服务
```typescript
// AI内容直接存储服务
class AIContentStorageService {
  // AI生成内容直接入库
  async createPostFromAI(
    aiContent: AIGeneratedPost,
    options: CreateOptions = {}
  ): Promise<BlogPost> {
    try {
      // 1. 内容预处理
      const processedContent = await this.preprocessContent(aiContent.content);

      // 2. 生成唯一slug
      const slug = await this.generateUniqueSlug(aiContent.title, aiContent.locale);

      // 3. 处理图片（如果有）
      const processedImages = await this.processImages(aiContent.images);

      // 4. SEO优化
      const seoData = await this.optimizeSEO(aiContent);

      // 5. 直接存储到数据库
      const post = await prisma.blogPost.create({
        data: {
          title: aiContent.title,
          slug,
          content: processedContent,
          excerpt: aiContent.excerpt || this.generateExcerpt(processedContent),
          locale: aiContent.locale,
          category: aiContent.category,
          tags: aiContent.tags,
          coverImage: processedImages?.coverImage,
          seoTitle: seoData.title,
          seoDescription: seoData.description,
          status: options.autoPublish ? 'PUBLISHED' : 'DRAFT',
          publishedAt: options.autoPublish ? new Date() : null,
          readingTime: this.calculateReadingTime(processedContent),
          // AI元数据存储在JSON字段中
          metadata: {
            ai: aiContent.aiMetadata,
            images: processedImages?.metadata
          }
        }
      });

      return post;

    } catch (error) {
      throw new Error(`Failed to create post from AI content: ${error.message}`);
    }
  }

  // 批量AI内容入库
  async batchCreateFromAI(
    aiContents: AIGeneratedPost[],
    options: BatchCreateOptions = {}
  ): Promise<BatchCreateResult> {
    const results: CreateResult[] = [];

    // 使用数据库事务确保一致性
    await prisma.$transaction(async (tx) => {
      for (const aiContent of aiContents) {
        try {
          const post = await this.createPostFromAI(aiContent, options);
          results.push({ success: true, post });

          // 批量处理间隔
          if (options.delay) {
            await this.delay(options.delay);
          }

        } catch (error) {
          results.push({
            success: false,
            error: error.message,
            title: aiContent.title
          });

          // 根据配置决定是否继续
          if (options.stopOnError) {
            throw error;
          }
        }
      }
    });

    return {
      total: aiContents.length,
      successful: results.filter(r => r.success).length,
      failed: results.filter(r => !r.success).length,
      results
    };
  }

  // 内容预处理
  private async preprocessContent(content: string): Promise<string> {
    // 1. HTML清理和验证
    // 2. 图片URL处理
    // 3. 内部链接优化
    // 4. 代码块格式化
    // 5. 返回处理后的内容
  }

  // 生成唯一slug
  private async generateUniqueSlug(title: string, locale: string): Promise<string> {
    // 1. 基础slug生成
    // 2. 检查数据库唯一性
    // 3. 添加数字后缀（如需要）
    // 4. 返回唯一slug
  }
}
```

## 内容质量检查系统
```typescript
// 内容质量检查器
class ContentQualityChecker {
  // 综合质量检查
  async checkContentQuality(post: BlogPost): Promise<QualityReport> {
    const checks = await Promise.all([
      this.checkSEO(post),
      this.checkReadability(post),
      this.checkImages(post),
      this.checkLinks(post),
      this.checkDuplicates(post)
    ]);
    
    return this.generateQualityReport(checks);
  }
  
  // SEO检查
  async checkSEO(post: BlogPost): Promise<SEOCheck> {
    return {
      titleLength: this.checkTitleLength(post.title),
      descriptionLength: this.checkDescriptionLength(post.description),
      keywordDensity: this.checkKeywordDensity(post.content, post.keywords),
      headingStructure: this.checkHeadingStructure(post.content),
      metaTags: this.checkMetaTags(post),
      score: this.calculateSEOScore(post)
    };
  }
  
  // 可读性检查
  async checkReadability(post: BlogPost): Promise<ReadabilityCheck> {
    return {
      wordCount: this.countWords(post.content),
      sentenceLength: this.analyzeSentenceLength(post.content),
      paragraphLength: this.analyzeParagraphLength(post.content),
      readingTime: this.calculateReadingTime(post.content),
      fleschScore: this.calculateFleschScore(post.content)
    };
  }
  
  // 图片检查
  async checkImages(post: BlogPost): Promise<ImageCheck> {
    return {
      altTags: this.checkAltTags(post.images),
      fileSize: this.checkImageSizes(post.images),
      format: this.checkImageFormats(post.images),
      loading: this.checkLazyLoading(post.images)
    };
  }
  
  // 重复内容检查
  async checkDuplicates(post: BlogPost): Promise<DuplicateCheck> {
    // 1. 检查标题重复
    // 2. 检查内容相似度
    // 3. 检查关键词重叠
    // 4. 生成重复度报告
  }
}
```

## 自动化发布系统
```typescript
// 自动发布调度器
class PublishingScheduler {
  // 定时发布
  async schedulePost(postId: string, publishDate: Date): Promise<void> {
    // 1. 创建定时任务
    // 2. 存储发布计划
    // 3. 设置提醒通知
  }
  
  // 批量发布
  async batchPublish(
    postIds: string[],
    options: BatchPublishOptions
  ): Promise<BatchPublishResult> {
    const results = [];
    
    for (const postId of postIds) {
      try {
        // 1. 最终质量检查
        const qualityCheck = await this.finalQualityCheck(postId);
        if (!qualityCheck.passed) {
          throw new Error(`Quality check failed: ${qualityCheck.issues.join(', ')}`);
        }
        
        // 2. 发布文章
        await this.publishPost(postId);
        
        // 3. 提交搜索引擎索引
        await this.submitToSearchEngines(postId);
        
        // 4. 社交媒体分享
        if (options.autoShare) {
          await this.shareToSocialMedia(postId);
        }
        
        // 5. 发送通知
        await this.sendPublishNotification(postId);
        
        results.push({ postId, success: true });
        
        // 6. 发布间隔控制
        if (options.interval) {
          await this.delay(options.interval);
        }
        
      } catch (error) {
        results.push({ 
          postId, 
          success: false, 
          error: error.message 
        });
      }
    }
    
    return { results, summary: this.generateSummary(results) };
  }
  
  // 发布后处理
  async postPublishProcessing(postId: string): Promise<void> {
    // 1. 生成社交媒体卡片
    // 2. 更新站点地图
    // 3. 清理缓存
    // 4. 发送Webhook通知
    // 5. 更新相关文章推荐
  }
}
```

## 简化的操作流程

### 方式一：AI直接生成入库（推荐）
1. **AI生成**：使用AI服务生成完整的博文内容
2. **直接入库**：AI内容直接存储到数据库，无需文件操作
3. **自动优化**：系统自动处理SEO、图片、slug等
4. **即时发布**：可选择立即发布或保存为草稿

### 方式二：管理后台创建
1. **后台编辑**：使用富文本编辑器创建内容
2. **实时预览**：编辑过程中实时预览效果
3. **SEO优化**：自动生成或手动设置SEO信息
4. **一键发布**：完成后直接发布到网站

### 方式三：API批量创建
1. **准备数据**：准备JSON格式的博文数据
2. **API调用**：通过REST API批量创建博文
3. **事务处理**：确保批量操作的数据一致性
4. **结果反馈**：返回详细的创建结果和错误信息

## 简化的API接口设计
```typescript
// 博客管理API（简化版）
const BLOG_API_ROUTES = {
  // 基础CRUD
  'GET /api/blog': '获取文章列表（支持分页、筛选、搜索）',
  'GET /api/blog/[slug]': '获取文章详情',
  'POST /api/blog': '创建文章（支持AI生成内容直接入库）',
  'PUT /api/blog/[id]': '更新文章',
  'DELETE /api/blog/[id]': '删除文章（软删除）',

  // AI内容生成
  'POST /api/blog/ai-generate': 'AI生成文章内容',
  'POST /api/blog/ai-batch-create': 'AI批量生成并创建文章',

  // 发布管理
  'PATCH /api/blog/[id]/publish': '发布文章',
  'PATCH /api/blog/[id]/unpublish': '取消发布',
  'POST /api/blog/batch-publish': '批量发布',

  // 内容优化
  'POST /api/blog/[id]/seo-optimize': 'SEO自动优化',
  'POST /api/blog/[id]/regenerate-excerpt': '重新生成摘要',
  'POST /api/blog/[id]/calculate-reading-time': '计算阅读时间',

  // 统计和分析
  'GET /api/blog/stats': '获取博客统计信息',
  'GET /api/blog/[id]/analytics': '获取单篇文章分析数据'
};
```

## 简化的数据库表结构
```sql
-- 主博文表（已在07-database-api-rules.md中定义）
-- 这里只需要添加一些优化索引和约束

-- 博文操作日志表（可选，用于审计）
CREATE TABLE blog_post_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  blog_post_id UUID REFERENCES blog_posts(id) ON DELETE CASCADE,
  action VARCHAR(50) NOT NULL, -- 'created', 'updated', 'published', 'deleted'
  user_id UUID REFERENCES users(id),
  changes JSONB, -- 记录变更内容
  created_at TIMESTAMP DEFAULT NOW()
);

-- 博文SEO分析表（可选，用于SEO优化跟踪）
CREATE TABLE blog_seo_analysis (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  blog_post_id UUID REFERENCES blog_posts(id) ON DELETE CASCADE,
  seo_score INTEGER CHECK (seo_score >= 0 AND seo_score <= 100),
  title_score INTEGER,
  description_score INTEGER,
  content_score INTEGER,
  keyword_density DECIMAL(5,2),
  suggestions JSONB,
  analyzed_at TIMESTAMP DEFAULT NOW()
);

-- 性能优化索引
CREATE INDEX idx_blog_posts_locale_category ON blog_posts(locale, category);
CREATE INDEX idx_blog_posts_status_published ON blog_posts(status, published_at);
CREATE INDEX idx_blog_posts_tags ON blog_posts USING GIN(tags);
CREATE INDEX idx_blog_posts_search ON blog_posts USING GIN(to_tsvector('english', title || ' ' || content));
```

## 玄学测试相关内容策略

### 测试导流博客内容
```typescript
// 测试导流内容策略
const TEST_TRAFFIC_CONTENT_STRATEGY = {
  // 内容类型规划
  contentTypes: {
    // 测试介绍文章
    testIntroduction: {
      purpose: '介绍测试原理和价值',
      structure: ['历史背景', '科学依据', '测试方法', '结果解读'],
      cta: '立即体验免费测试',
      seoTarget: '测试相关长尾关键词'
    },

    // 测试结果解读
    resultInterpretation: {
      purpose: '深度解读测试结果含义',
      structure: ['结果分类', '性格特质', '生活指导', '发展建议'],
      cta: '获取个性化测试结果',
      seoTarget: '结果解读相关关键词'
    },

    // 测试案例分析
    caseStudies: {
      purpose: '真实用户测试案例分享',
      structure: ['用户背景', '测试过程', '结果分析', '后续发展'],
      cta: '分享你的测试故事',
      seoTarget: '案例分析相关关键词'
    },

    // 测试技巧指南
    testGuides: {
      purpose: '如何更好地进行测试',
      structure: ['准备工作', '注意事项', '结果理解', '应用建议'],
      cta: '开始专业测试',
      seoTarget: '测试技巧相关关键词'
    }
  },

  // 内容与测试的关联策略
  contentTestIntegration: {
    // 内容中的测试推荐
    inContentRecommendations: {
      placement: ['文章开头', '段落之间', '文章结尾'],
      format: ['内联推荐', '卡片式推荐', 'CTA按钮'],
      personalization: '基于文章主题推荐相关测试'
    },

    // 测试结果内容化
    resultContentization: {
      blogSeries: '基于热门测试结果创建博客系列',
      userStories: '用户测试故事内容化',
      trendAnalysis: '测试趋势分析文章'
    }
  }
};
```

### SEO内容优化策略
```typescript
// 玄学测试SEO内容策略
const MYSTICAL_SEO_CONTENT_STRATEGY = {
  // 关键词策略
  keywordStrategy: {
    // 核心关键词
    coreKeywords: [
      '塔罗牌测试', '星座测试', '数字命理', '玄学测试',
      '性格分析', '运势预测', '免费测试', 'AI分析'
    ],

    // 长尾关键词
    longTailKeywords: [
      '免费在线塔罗牌测试准确',
      '星座性格分析测试',
      '数字命理生命密码计算',
      '水晶能量测试选择',
      '手相解读在线测试',
      '梦境解析测试分析'
    ],

    // 问题型关键词
    questionKeywords: [
      '塔罗牌测试准确吗',
      '如何进行星座测试',
      '数字命理怎么算',
      '什么是水晶能量测试',
      '手相能看出什么',
      '梦境代表什么意思'
    ]
  },

  // 内容集群策略
  contentClusters: {
    // 塔罗牌内容集群
    tarotCluster: {
      pillarPage: '塔罗牌完全指南',
      supportingContent: [
        '塔罗牌历史起源',
        '78张塔罗牌含义解读',
        '塔罗牌占卜方法',
        '塔罗牌测试准确性分析',
        '如何选择塔罗牌',
        '塔罗牌与心理学'
      ],
      testIntegration: '每篇文章都链接到相关塔罗测试'
    },

    // 星座内容集群
    astrologyCluster: {
      pillarPage: '星座占星学大全',
      supportingContent: [
        '12星座性格特点详解',
        '星座配对指南',
        '星座运势分析方法',
        '上升星座的重要性',
        '星座与职业选择',
        '星座心理学研究'
      ],
      testIntegration: '引导用户进行星座性格测试'
    },

    // 数字命理内容集群
    numerologyCluster: {
      pillarPage: '数字命理学入门',
      supportingContent: [
        '生命数字计算方法',
        '数字的神秘含义',
        '姓名数字学分析',
        '数字与性格关系',
        '数字命理历史发展',
        '现代数字命理应用'
      ],
      testIntegration: '提供免费数字命理计算工具'
    }
  }
};
```

### 内容生产工作流
```typescript
// 测试相关内容生产流程
const TEST_CONTENT_PRODUCTION_WORKFLOW = {
  // 内容规划阶段
  planning: {
    marketResearch: {
      keywordAnalysis: '关键词搜索量和竞争度分析',
      competitorAnalysis: '竞争对手内容分析',
      userIntent: '用户搜索意图分析',
      contentGaps: '内容空白点识别'
    },

    contentCalendar: {
      testLaunches: '配合新测试上线的内容计划',
      seasonalContent: '节日和季节性内容',
      trendingTopics: '热门话题内容',
      evergreen: '常青内容规划'
    }
  },

  // 内容创作阶段
  creation: {
    aiAssisted: {
      topicGeneration: 'AI生成内容主题',
      outlineCreation: 'AI辅助创建文章大纲',
      contentDrafting: 'AI生成初稿内容',
      optimization: 'AI优化内容质量'
    },

    humanReview: {
      factChecking: '事实准确性检查',
      culturalSensitivity: '文化敏感性审核',
      brandAlignment: '品牌调性一致性',
      testIntegration: '测试链接和CTA优化'
    }
  },

  // 内容发布阶段
  publishing: {
    seoOptimization: {
      metaTags: 'SEO元标签优化',
      internalLinking: '内部链接策略',
      imageOptimization: '图片SEO优化',
      structuredData: '结构化数据标记'
    },

    crossPromotion: {
      testRecommendations: '文章中推荐相关测试',
      socialSharing: '社交媒体分享优化',
      emailMarketing: '邮件营销内容',
      communityEngagement: '社区互动内容'
    }
  }
};
```

### 内容效果监控
```typescript
// 测试导流内容效果监控
const CONTENT_PERFORMANCE_MONITORING = {
  // 关键指标
  keyMetrics: {
    trafficMetrics: {
      organicTraffic: '自然搜索流量',
      directTraffic: '直接访问流量',
      referralTraffic: '推荐流量',
      socialTraffic: '社交媒体流量'
    },

    engagementMetrics: {
      timeOnPage: '页面停留时间',
      bounceRate: '跳出率',
      pageViews: '页面浏览量',
      socialShares: '社交分享次数'
    },

    conversionMetrics: {
      testClickthrough: '测试点击率',
      testCompletion: '测试完成率',
      testSharing: '测试分享率',
      returnVisits: '回访率'
    }
  },

  // 内容优化循环
  optimizationLoop: {
    dataCollection: '数据收集和分析',
    insightGeneration: '洞察提取',
    contentIteration: '内容迭代优化',
    performanceTracking: '效果跟踪验证'
  }
};
```

## 博客页面UI/UX设计规范

### 博客列表页面设计
```typescript
// 博客列表页面完整设计规范 - 对标Medium
const BLOG_LIST_PAGE_DESIGN = {
  // 页面布局
  layout: {
    container: {
      maxWidth: '1200px',
      margin: '0 auto',
      padding: { mobile: '1rem', desktop: '2rem' }
    },

    // 页面头部
    header: {
      marginBottom: '3rem',

      // 页面标题
      title: {
        fontSize: { mobile: '2.5rem', desktop: '3rem' },
        fontWeight: 'bold',
        fontFamily: 'serif',
        color: 'mystical-900 dark:white',
        textAlign: 'center',
        marginBottom: '1rem',
        letterSpacing: '-0.02em'
      },

      // 页面描述
      description: {
        fontSize: { mobile: '1.125rem', desktop: '1.25rem' },
        color: 'mystical-600 dark:mystical-300',
        textAlign: 'center',
        maxWidth: '600px',
        margin: '0 auto',
        lineHeight: '1.6'
      },

      // 分类导航
      categoryNav: {
        marginTop: '2rem',
        display: 'flex',
        justifyContent: 'center',
        flexWrap: 'wrap',
        gap: '0.75rem',

        item: {
          fontSize: '0.875rem',
          fontWeight: 'medium',
          color: 'mystical-600 dark:mystical-400',
          background: 'mystical-100 dark:dark-700',
          padding: '0.75rem 1.5rem',
          borderRadius: 'full',
          textDecoration: 'none',
          border: '1px solid mystical-200 dark:dark-600',
          transition: 'all 0.2s ease',

          hover: {
            background: 'mystical-200 dark:dark-600',
            color: 'mystical-700 dark:mystical-300',
            transform: 'translateY(-1px)'
          },

          active: {
            background: 'mystical-500',
            color: 'white',
            borderColor: 'mystical-500'
          }
        }
      }
    },

    // 主要内容区域
    main: {
      display: 'grid',
      gridTemplateColumns: {
        mobile: '1fr',
        tablet: '1fr',
        desktop: '2fr 1fr'
      },
      gap: '3rem',
      alignItems: 'start'
    }
  },

  // 文章列表区域
  articleList: {
    // 特色文章（第一篇）
    featuredPost: {
      marginBottom: '3rem',
      padding: '2rem',
      background: 'gradient-to-br from-mystical-50 to-gold-50 dark:from-dark-800 dark:to-dark-700',
      borderRadius: '2xl',
      border: '1px solid mystical-200 dark:dark-600',

      image: {
        width: '100%',
        aspectRatio: '16:9',
        borderRadius: 'xl',
        marginBottom: '1.5rem',
        objectFit: 'cover'
      },

      category: {
        fontSize: '0.75rem',
        fontWeight: 'semibold',
        color: 'mystical-600',
        textTransform: 'uppercase',
        letterSpacing: '0.05em',
        marginBottom: '0.5rem'
      },

      title: {
        fontSize: { mobile: '1.75rem', desktop: '2.25rem' },
        fontWeight: 'bold',
        fontFamily: 'serif',
        color: 'mystical-900 dark:white',
        lineHeight: '1.2',
        marginBottom: '1rem',

        link: {
          textDecoration: 'none',
          hover: 'color-mystical-700'
        }
      },

      excerpt: {
        fontSize: { mobile: '1rem', desktop: '1.125rem' },
        color: 'mystical-600 dark:mystical-300',
        lineHeight: '1.6',
        marginBottom: '1.5rem',
        display: '-webkit-box',
        webkitLineClamp: '3',
        webkitBoxOrient: 'vertical',
        overflow: 'hidden'
      },

      meta: {
        display: 'flex',
        alignItems: 'center',
        gap: '1rem',
        fontSize: '0.875rem',
        color: 'mystical-500 dark:mystical-400',

        author: {
          display: 'flex',
          alignItems: 'center',
          gap: '0.5rem',

          avatar: {
            width: '32px',
            height: '32px',
            borderRadius: 'full'
          }
        },

        date: { format: 'MMM DD, YYYY' },
        readTime: { format: 'X min read' }
      }
    },

    // 常规文章列表
    regularPosts: {
      display: 'grid',
      gap: '2rem',

      // 文章卡片
      postCard: {
        display: 'grid',
        gridTemplateColumns: { mobile: '1fr', tablet: '200px 1fr' },
        gap: '1.5rem',
        padding: '1.5rem',
        background: 'white dark:dark-800',
        borderRadius: 'xl',
        border: '1px solid mystical-200 dark:dark-700',
        transition: 'all 0.3s ease',

        hover: {
          transform: 'translateY(-2px)',
          boxShadow: 'mystical-lg',
          borderColor: 'mystical-300 dark:dark-500'
        },

        image: {
          width: '100%',
          aspectRatio: '16:9',
          borderRadius: 'lg',
          objectFit: 'cover'
        },

        content: {
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'space-between',

          header: {
            category: {
              fontSize: '0.75rem',
              fontWeight: 'semibold',
              color: 'mystical-600',
              textTransform: 'uppercase',
              letterSpacing: '0.05em',
              marginBottom: '0.5rem'
            },

            title: {
              fontSize: { mobile: '1.25rem', desktop: '1.5rem' },
              fontWeight: 'bold',
              fontFamily: 'serif',
              color: 'mystical-900 dark:white',
              lineHeight: '1.3',
              marginBottom: '0.75rem',

              link: {
                textDecoration: 'none',
                hover: 'color-mystical-700'
              }
            },

            excerpt: {
              fontSize: '0.875rem',
              color: 'mystical-600 dark:mystical-300',
              lineHeight: '1.5',
              marginBottom: '1rem',
              display: '-webkit-box',
              webkitLineClamp: '2',
              webkitBoxOrient: 'vertical',
              overflow: 'hidden'
            }
          },

          footer: {
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            fontSize: '0.75rem',
            color: 'mystical-500 dark:mystical-400',

            meta: {
              display: 'flex',
              alignItems: 'center',
              gap: '0.75rem'
            },

            readMore: {
              fontSize: '0.875rem',
              color: 'mystical-600',
              textDecoration: 'none',
              fontWeight: 'medium',

              hover: {
                color: 'mystical-700',
                textDecoration: 'underline'
              }
            }
          }
        }
      }
    },

    // 分页导航
    pagination: {
      marginTop: '3rem',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      gap: '0.5rem',

      button: {
        width: '40px',
        height: '40px',
        borderRadius: 'lg',
        border: '1px solid mystical-200 dark:dark-600',
        background: 'white dark:dark-800',
        color: 'mystical-600 dark:mystical-400',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        textDecoration: 'none',
        fontSize: '0.875rem',
        fontWeight: 'medium',
        transition: 'all 0.2s ease',

        hover: {
          background: 'mystical-50 dark:dark-700',
          borderColor: 'mystical-300 dark:dark-500',
          color: 'mystical-700 dark:mystical-300'
        },

        active: {
          background: 'mystical-500',
          borderColor: 'mystical-500',
          color: 'white'
        },

        disabled: {
          opacity: '0.5',
          cursor: 'not-allowed'
        }
      }
    }
  },

  // 侧边栏
  sidebar: {
    position: 'sticky',
    top: '2rem',

    // 搜索框
    search: {
      marginBottom: '2rem',

      container: {
        position: 'relative'
      },

      input: {
        width: '100%',
        padding: '0.75rem 1rem 0.75rem 2.5rem',
        fontSize: '0.875rem',
        border: '1px solid mystical-200 dark:dark-600',
        borderRadius: 'lg',
        background: 'white dark:dark-800',
        color: 'mystical-800 dark:mystical-200',

        placeholder: {
          color: 'mystical-400 dark:mystical-500'
        },

        focus: {
          outline: 'none',
          borderColor: 'mystical-400',
          boxShadow: '0 0 0 3px rgba(168, 85, 247, 0.1)'
        }
      },

      icon: {
        position: 'absolute',
        left: '0.75rem',
        top: '50%',
        transform: 'translateY(-50%)',
        color: 'mystical-400',
        width: '16px',
        height: '16px'
      }
    },

    // 热门文章
    popularPosts: {
      marginBottom: '2rem',

      title: {
        fontSize: '1.125rem',
        fontWeight: 'bold',
        color: 'mystical-900 dark:white',
        marginBottom: '1rem'
      },

      list: {
        display: 'flex',
        flexDirection: 'column',
        gap: '1rem',

        item: {
          display: 'flex',
          gap: '0.75rem',

          number: {
            fontSize: '0.75rem',
            fontWeight: 'bold',
            color: 'mystical-500',
            minWidth: '20px'
          },

          content: {
            title: {
              fontSize: '0.875rem',
              fontWeight: 'medium',
              color: 'mystical-800 dark:mystical-200',
              lineHeight: '1.4',
              marginBottom: '0.25rem',

              link: {
                textDecoration: 'none',
                hover: 'color-mystical-600'
              }
            },

            meta: {
              fontSize: '0.75rem',
              color: 'mystical-500 dark:mystical-400'
            }
          }
        }
      }
    },

    // 分类标签云
    tagCloud: {
      marginBottom: '2rem',

      title: {
        fontSize: '1.125rem',
        fontWeight: 'bold',
        color: 'mystical-900 dark:white',
        marginBottom: '1rem'
      },

      tags: {
        display: 'flex',
        flexWrap: 'wrap',
        gap: '0.5rem',

        tag: {
          fontSize: '0.75rem',
          color: 'mystical-600 dark:mystical-400',
          background: 'mystical-100 dark:dark-700',
          padding: '0.25rem 0.75rem',
          borderRadius: 'full',
          textDecoration: 'none',
          border: '1px solid mystical-200 dark:dark-600',

          hover: {
            background: 'mystical-200 dark:dark-600',
            color: 'mystical-700 dark:mystical-300'
          }
        }
      }
    },

    // 订阅表单
    newsletter: {
      background: 'mystical-50 dark:dark-800',
      padding: '1.5rem',
      borderRadius: 'xl',
      border: '1px solid mystical-200 dark:dark-700',

      title: {
        fontSize: '1.125rem',
        fontWeight: 'bold',
        color: 'mystical-900 dark:white',
        marginBottom: '0.5rem'
      },

      description: {
        fontSize: '0.875rem',
        color: 'mystical-600 dark:mystical-300',
        marginBottom: '1rem',
        lineHeight: '1.5'
      },

      form: {
        display: 'flex',
        flexDirection: 'column',
        gap: '0.75rem',

        input: {
          padding: '0.75rem',
          fontSize: '0.875rem',
          border: '1px solid mystical-200 dark:dark-600',
          borderRadius: 'lg',
          background: 'white dark:dark-700',
          color: 'mystical-800 dark:mystical-200',

          focus: {
            outline: 'none',
            borderColor: 'mystical-400',
            boxShadow: '0 0 0 3px rgba(168, 85, 247, 0.1)'
          }
        },

        button: {
          padding: '0.75rem',
          fontSize: '0.875rem',
          fontWeight: 'medium',
          background: 'mystical-500',
          color: 'white',
          border: 'none',
          borderRadius: 'lg',
          cursor: 'pointer',
          transition: 'all 0.2s ease',

          hover: {
            background: 'mystical-600',
            transform: 'translateY(-1px)'
          }
        }
      }
    }
  },

  // 响应式适配
  responsive: {
    mobile: {
      layout: {
        main: {
          gridTemplateColumns: '1fr',
          gap: '2rem'
        }
      },

      featuredPost: {
        padding: '1.5rem'
      },

      postCard: {
        gridTemplateColumns: '1fr',
        padding: '1rem'
      },

      sidebar: {
        order: '-1',
        position: 'static'
      }
    }
  }
};
```

### 博客阅读体验优化规范
```typescript
// 博客阅读体验优化 - 基于Medium最佳实践
const BLOG_READING_EXPERIENCE = {
  // 阅读进度指示器
  readingProgress: {
    // 顶部进度条
    topProgressBar: {
      position: 'fixed',
      top: '0',
      left: '0',
      width: '100%',
      height: '3px',
      background: 'mystical-500',
      zIndex: '100',
      transformOrigin: 'left',
      transition: 'transform 0.1s ease'
    },

    // 侧边进度圆环
    sideProgressRing: {
      position: 'fixed',
      right: '2rem',
      bottom: '2rem',
      width: '60px',
      height: '60px',
      borderRadius: 'full',
      background: 'white dark:dark-800',
      border: '1px solid mystical-200 dark:dark-600',
      boxShadow: 'mystical',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: '50',

      // 进度环
      progressRing: {
        width: '40px',
        height: '40px',
        borderRadius: 'full',
        border: '3px solid mystical-100',
        borderTop: '3px solid mystical-500',
        transition: 'all 0.3s ease'
      },

      // 百分比文字
      percentage: {
        position: 'absolute',
        fontSize: '0.75rem',
        fontWeight: 'bold',
        color: 'mystical-600'
      }
    }
  },

  // 目录导航
  tableOfContents: {
    // 浮动目录
    floating: {
      position: 'fixed',
      left: '2rem',
      top: '50%',
      transform: 'translateY(-50%)',
      maxWidth: '200px',
      background: 'white dark:dark-800',
      border: '1px solid mystical-200 dark:dark-600',
      borderRadius: 'lg',
      padding: '1rem',
      boxShadow: 'mystical',
      zIndex: '40',

      // 仅在桌面端显示
      display: { mobile: 'none', desktop: 'block' },

      title: {
        fontSize: '0.875rem',
        fontWeight: 'bold',
        color: 'mystical-900 dark:white',
        marginBottom: '0.75rem',
        paddingBottom: '0.5rem',
        borderBottom: '1px solid mystical-200 dark:dark-600'
      },

      list: {
        display: 'flex',
        flexDirection: 'column',
        gap: '0.25rem',

        item: {
          fontSize: '0.75rem',
          color: 'mystical-600 dark:mystical-400',
          textDecoration: 'none',
          padding: '0.25rem 0',
          borderLeft: '2px solid transparent',
          paddingLeft: '0.5rem',
          transition: 'all 0.2s ease',

          hover: {
            color: 'mystical-700 dark:mystical-300',
            borderLeftColor: 'mystical-300'
          },

          active: {
            color: 'mystical-800 dark:white',
            borderLeftColor: 'mystical-500',
            fontWeight: 'medium'
          },

          // 层级缩进
          level2: { paddingLeft: '1rem' },
          level3: { paddingLeft: '1.5rem' }
        }
      }
    },

    // 移动端目录
    mobile: {
      position: 'sticky',
      top: '80px',
      background: 'mystical-50 dark:dark-800',
      border: '1px solid mystical-200 dark:dark-600',
      borderRadius: 'lg',
      padding: '1rem',
      marginBottom: '2rem',

      // 可折叠
      collapsible: true,

      toggle: {
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        cursor: 'pointer',

        title: {
          fontSize: '0.875rem',
          fontWeight: 'bold',
          color: 'mystical-900 dark:white'
        },

        icon: {
          width: '16px',
          height: '16px',
          color: 'mystical-600',
          transition: 'transform 0.2s ease'
        }
      }
    }
  },

  // 文章互动功能
  interactions: {
    // 点赞/收藏按钮
    engagementButtons: {
      position: 'fixed',
      left: '2rem',
      bottom: '2rem',
      display: 'flex',
      flexDirection: 'column',
      gap: '0.75rem',
      zIndex: '50',

      // 仅在桌面端显示
      display: { mobile: 'none', desktop: 'flex' },

      button: {
        width: '48px',
        height: '48px',
        borderRadius: 'full',
        background: 'white dark:dark-800',
        border: '1px solid mystical-200 dark:dark-600',
        boxShadow: 'mystical',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        cursor: 'pointer',
        transition: 'all 0.3s ease',

        hover: {
          transform: 'translateY(-2px)',
          boxShadow: 'mystical-lg'
        },

        icon: {
          width: '20px',
          height: '20px',
          color: 'mystical-600 dark:mystical-400'
        },

        // 激活状态
        active: {
          background: 'mystical-500',
          borderColor: 'mystical-500',

          icon: {
            color: 'white'
          }
        }
      }
    },

    // 分享菜单
    shareMenu: {
      position: 'relative',

      trigger: {
        width: '48px',
        height: '48px',
        borderRadius: 'full',
        background: 'white dark:dark-800',
        border: '1px solid mystical-200 dark:dark-600',
        boxShadow: 'mystical',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        cursor: 'pointer'
      },

      dropdown: {
        position: 'absolute',
        bottom: '100%',
        left: '0',
        marginBottom: '0.5rem',
        background: 'white dark:dark-800',
        border: '1px solid mystical-200 dark:dark-600',
        borderRadius: 'lg',
        padding: '0.5rem',
        boxShadow: 'mystical-lg',
        minWidth: '160px',

        item: {
          display: 'flex',
          alignItems: 'center',
          gap: '0.75rem',
          padding: '0.75rem',
          fontSize: '0.875rem',
          color: 'mystical-700 dark:mystical-300',
          textDecoration: 'none',
          borderRadius: 'md',
          transition: 'all 0.2s ease',

          hover: {
            background: 'mystical-50 dark:dark-700'
          },

          icon: {
            width: '16px',
            height: '16px'
          }
        }
      }
    },

    // 评论系统
    comments: {
      marginTop: '4rem',
      paddingTop: '2rem',
      borderTop: '1px solid mystical-200 dark:dark-700',

      header: {
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginBottom: '2rem',

        title: {
          fontSize: '1.5rem',
          fontWeight: 'bold',
          color: 'mystical-900 dark:white'
        },

        count: {
          fontSize: '0.875rem',
          color: 'mystical-500 dark:mystical-400'
        }
      },

      // 评论表单
      form: {
        marginBottom: '2rem',

        textarea: {
          width: '100%',
          minHeight: '120px',
          padding: '1rem',
          fontSize: '1rem',
          border: '1px solid mystical-200 dark:dark-600',
          borderRadius: 'lg',
          background: 'white dark:dark-800',
          color: 'mystical-800 dark:mystical-200',
          resize: 'vertical',

          focus: {
            outline: 'none',
            borderColor: 'mystical-400',
            boxShadow: '0 0 0 3px rgba(168, 85, 247, 0.1)'
          }
        },

        actions: {
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginTop: '1rem',

          guidelines: {
            fontSize: '0.75rem',
            color: 'mystical-500 dark:mystical-400'
          },

          submitButton: {
            padding: '0.75rem 1.5rem',
            fontSize: '0.875rem',
            fontWeight: 'medium',
            background: 'mystical-500',
            color: 'white',
            border: 'none',
            borderRadius: 'lg',
            cursor: 'pointer',

            hover: {
              background: 'mystical-600'
            }
          }
        }
      },

      // 评论列表
      list: {
        display: 'flex',
        flexDirection: 'column',
        gap: '1.5rem',

        comment: {
          padding: '1.5rem',
          background: 'mystical-50 dark:dark-800',
          borderRadius: 'lg',
          border: '1px solid mystical-200 dark:dark-600',

          header: {
            display: 'flex',
            alignItems: 'center',
            gap: '0.75rem',
            marginBottom: '1rem',

            avatar: {
              width: '40px',
              height: '40px',
              borderRadius: 'full'
            },

            info: {
              name: {
                fontSize: '0.875rem',
                fontWeight: 'semibold',
                color: 'mystical-900 dark:white'
              },

              date: {
                fontSize: '0.75rem',
                color: 'mystical-500 dark:mystical-400'
              }
            }
          },

          content: {
            fontSize: '0.875rem',
            color: 'mystical-700 dark:mystical-300',
            lineHeight: '1.6',
            marginBottom: '1rem'
          },

          actions: {
            display: 'flex',
            gap: '1rem',

            button: {
              fontSize: '0.75rem',
              color: 'mystical-500 dark:mystical-400',
              background: 'none',
              border: 'none',
              cursor: 'pointer',

              hover: {
                color: 'mystical-600 dark:mystical-300'
              }
            }
          }
        }
      }
    }
  },

  // 阅读模式
  readingMode: {
    // 专注阅读模式
    focusMode: {
      trigger: {
        position: 'fixed',
        top: '50%',
        right: '1rem',
        transform: 'translateY(-50%)',
        width: '40px',
        height: '40px',
        borderRadius: 'full',
        background: 'white dark:dark-800',
        border: '1px solid mystical-200 dark:dark-600',
        boxShadow: 'mystical',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        cursor: 'pointer',
        zIndex: '60'
      },

      // 激活时的样式变化
      active: {
        // 隐藏侧边栏和其他干扰元素
        hideSidebar: true,
        hideNavigation: true,
        hideComments: true,

        // 调整内容宽度
        contentMaxWidth: '800px',

        // 增加行间距
        lineHeight: '1.8',

        // 调整字体大小
        fontSize: '1.125rem'
      }
    },

    // 夜间模式切换
    darkModeToggle: {
      position: 'fixed',
      top: '1rem',
      right: '1rem',
      width: '40px',
      height: '40px',
      borderRadius: 'full',
      background: 'white dark:dark-800',
      border: '1px solid mystical-200 dark:dark-600',
      boxShadow: 'mystical',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      cursor: 'pointer',
      zIndex: '60',

      icon: {
        width: '20px',
        height: '20px',
        color: 'mystical-600 dark:mystical-400',
        transition: 'all 0.3s ease'
      }
    }
  },

  // 性能优化
  performance: {
    // 图片懒加载
    lazyLoading: {
      threshold: '50px',
      placeholder: 'blur',
      quality: 85
    },

    // 代码高亮懒加载
    codeHighlighting: {
      loadOnDemand: true,
      supportedLanguages: ['javascript', 'typescript', 'python', 'css', 'html']
    },

    // 评论懒加载
    commentsLazyLoad: {
      loadOnScroll: true,
      threshold: '200px'
    }
  }
};
```
