// API响应格式工具函数
// API Response Format Utilities

import { NextResponse } from 'next/server'

// 统一响应格式类型定义
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  meta?: ResponseMeta
}

export interface ApiErrorResponse {
  success: false
  error: {
    code: string
    message: string
    details?: any
  }
}

export interface ResponseMeta {
  pagination?: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
  timestamp?: string
  requestId?: string
}

// 错误代码常量
export const ErrorCodes = {
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  UNAUTHORIZED: 'UNAUTHORIZED',
  FORBIDDEN: 'FORBIDDEN',
  NOT_FOUND: 'NOT_FOUND',
  RATE_LIMITED: 'RATE_LIMITED',
  INTERNAL_ERROR: 'INTERNAL_ERROR',
  DATABASE_ERROR: 'DATABASE_ERROR',
  INVALID_JSON: 'INVALID_JSON'
} as const

export type ErrorCode = typeof ErrorCodes[keyof typeof ErrorCodes]

// 成功响应函数
export function successResponse<T>(
  data: T,
  message?: string,
  meta?: ResponseMeta,
  status: number = 200
): NextResponse<ApiResponse<T>> {
  return NextResponse.json(
    {
      success: true,
      data,
      message,
      meta: {
        ...meta,
        timestamp: new Date().toISOString()
      }
    },
    { status }
  )
}

// 错误响应函数
export function errorResponse(
  code: ErrorCode,
  message: string,
  details?: any,
  status: number = 400
): NextResponse<ApiErrorResponse> {
  return NextResponse.json(
    {
      success: false,
      error: {
        code,
        message,
        details
      }
    },
    { status }
  )
}

// 分页响应函数
export function paginatedResponse<T>(
  data: T[],
  page: number,
  limit: number,
  total: number,
  message?: string
): NextResponse<ApiResponse<T[]>> {
  const totalPages = Math.ceil(total / limit)
  const hasNext = page < totalPages
  const hasPrev = page > 1

  return successResponse(
    data,
    message,
    {
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext,
        hasPrev
      }
    }
  )
}

// 常用错误响应快捷函数
export const ApiErrors = {
  validation: (message: string, details?: any) =>
    errorResponse(ErrorCodes.VALIDATION_ERROR, message, details, 400),
  
  unauthorized: (message: string = 'Unauthorized access') =>
    errorResponse(ErrorCodes.UNAUTHORIZED, message, undefined, 401),
  
  forbidden: (message: string = 'Access forbidden') =>
    errorResponse(ErrorCodes.FORBIDDEN, message, undefined, 403),
  
  notFound: (message: string = 'Resource not found') =>
    errorResponse(ErrorCodes.NOT_FOUND, message, undefined, 404),
  
  rateLimited: (message: string = 'Rate limit exceeded') =>
    errorResponse(ErrorCodes.RATE_LIMITED, message, undefined, 429),
  
  internal: (message: string = 'Internal server error', details?: any) =>
    errorResponse(ErrorCodes.INTERNAL_ERROR, message, details, 500),
  
  database: (message: string = 'Database error', details?: any) =>
    errorResponse(ErrorCodes.DATABASE_ERROR, message, details, 500),
  
  invalidJson: (message: string = 'Invalid JSON format') =>
    errorResponse(ErrorCodes.INVALID_JSON, message, undefined, 400)
}

// 异常处理包装器
export function withErrorHandling<T extends any[], R>(
  handler: (...args: T) => Promise<R>
) {
  return async (...args: T): Promise<R | NextResponse<ApiErrorResponse>> => {
    try {
      return await handler(...args)
    } catch (error) {
      console.error('API Error:', error)
      
      if (error instanceof Error) {
        return ApiErrors.internal('An unexpected error occurred', {
          message: error.message,
          stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
        })
      }
      
      return ApiErrors.internal('An unknown error occurred')
    }
  }
}

// JSON解析工具函数
export function parseJsonField<T = any>(jsonString: string | null): T | null {
  if (!jsonString) return null
  
  try {
    return JSON.parse(jsonString) as T
  } catch (error) {
    console.error('JSON parsing error:', error)
    return null
  }
}

// JSON序列化工具函数
export function stringifyJsonField(data: any): string {
  try {
    return JSON.stringify(data)
  } catch (error) {
    console.error('JSON stringify error:', error)
    return '{}'
  }
}
