// Prisma Schema for Mystical Website
// 玄学网站数据库模型定义 - Cloudflare D1 (SQLite)

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// 用户表
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  username  String?  @unique
  avatar    String?
  locale    String   @default("en")
  theme     String   @default("light")
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关联关系
  testResults   TestResult[]
  blogViews     BlogView[]
  comments      Comment[]
  favorites     UserFavorite[]
  sessions      UserSession[]
  verifications UserVerification[]

  @@map("users")
}

// 博客文章表（优化的纯数据库存储版本 - D1/SQLite适配）
model BlogPost {
  id          String    @id @default(cuid())
  title       String
  slug        String    @unique
  content     String // 支持富文本HTML内容
  excerpt     String?
  coverImage  String?
  locale      String
  category    String
  tags        String    @default("[]") // JSON数组格式: ["tag1", "tag2"]，使用D1 JSON函数查询
  status      String    @default("DRAFT") // DRAFT, PENDING, SCHEDULED, PUBLISHED, ARCHIVED, DELETED
  publishedAt DateTime?
  scheduledAt DateTime? // 定时发布时间
  viewCount   Int       @default(0)
  likeCount   Int       @default(0)
  shareCount  Int       @default(0)
  readingTime Int       @default(0) // 分钟数
  featured    Boolean   @default(false) // 是否为特色文章

  // SEO优化字段
  seoTitle       String?
  seoDescription String?
  keywords       String  @default("[]") // JSON数组格式: ["keyword1", "keyword2"]

  // AI生成元数据（JSON格式）
  aiMetadata String? // JSON对象：{model, generatedAt, prompt, confidence, etc.}

  // 扩展元数据（JSON格式，存储其他信息）
  metadata String? // JSON对象格式，利用D1原生JSON函数

  // 时间戳
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // 关联关系
  views     BlogView[]
  comments  Comment[]
  favorites UserFavorite[]

  // D1优化索引策略
  @@index([locale, category])
  @@index([status, publishedAt])
  @@index([locale, status, publishedAt])
  @@index([featured, publishedAt])
  @@index([slug]) // 单独为slug创建索引，提高查询性能
  @@map("blog_posts")
}

// 测试结果表（D1优化版本）
model TestResult {
  id         String   @id @default(cuid())
  userId     String?
  testType   String // TAROT, ASTROLOGY, NUMEROLOGY, CRYSTAL, PALMISTRY, DREAMS
  answers    String // JSON字符串格式，存储测试答案
  result     String // JSON字符串格式，存储测试结果
  shareToken String?  @unique
  isPublic   Boolean  @default(false)
  createdAt  DateTime @default(now())

  // 关联关系
  user User? @relation(fields: [userId], references: [id])

  // D1索引优化
  @@index([userId, testType])
  @@index([shareToken])
  @@index([testType, createdAt])
  @@map("test_results")
}

// 评论表（D1优化版本）
model Comment {
  id         String   @id @default(cuid())
  content    String // 移除@db.Text，SQLite自动处理
  userId     String?
  postId     String
  parentId   String? // 回复评论的父评论ID
  isApproved Boolean  @default(false)
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  // 关联关系
  user    User?     @relation(fields: [userId], references: [id])
  post    BlogPost  @relation(fields: [postId], references: [id], onDelete: Cascade)
  parent  Comment?  @relation("CommentReplies", fields: [parentId], references: [id])
  replies Comment[] @relation("CommentReplies")

  // D1索引优化
  @@index([postId, createdAt])
  @@index([userId])
  @@index([parentId])
  @@map("comments")
}

// 用户收藏表
model UserFavorite {
  id        String   @id @default(cuid())
  userId    String
  postId    String
  createdAt DateTime @default(now())

  // 关联关系
  user User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  post BlogPost @relation(fields: [postId], references: [id], onDelete: Cascade)

  // 唯一约束：用户不能重复收藏同一篇文章
  @@unique([userId, postId])
  @@map("user_favorites")
}

// 博客浏览记录表
model BlogView {
  id        String   @id @default(cuid())
  postId    String
  userId    String? // 可选，游客访问时为null
  ipAddress String? // 用于统计唯一访问
  userAgent String?
  createdAt DateTime @default(now())

  // 关联关系
  post BlogPost @relation(fields: [postId], references: [id], onDelete: Cascade)
  user User?    @relation(fields: [userId], references: [id])

  @@map("blog_views")
}

// 用户会话表
model UserSession {
  id           String   @id @default(cuid())
  userId       String
  sessionToken String   @unique
  refreshToken String   @unique
  expiresAt    DateTime
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // 关联关系
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_sessions")
}

// 用户验证表（邮箱验证、密码重置等）
model UserVerification {
  id        String   @id @default(cuid())
  userId    String?
  email     String?
  token     String   @unique
  type      String // EMAIL_VERIFICATION, PASSWORD_RESET, ACCOUNT_DELETION
  expiresAt DateTime
  used      Boolean  @default(false)
  createdAt DateTime @default(now())

  // 关联关系
  user User? @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_verifications")
}
