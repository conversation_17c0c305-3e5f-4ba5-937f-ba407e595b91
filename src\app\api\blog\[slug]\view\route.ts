// 博客文章浏览记录API
// Blog Post View Tracking API

import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { 
  successResponse, 
  ApiErrors, 
  withErrorHandling
} from '@/lib/api-response'

interface RouteParams {
  params: {
    slug: string
  }
}

// POST /api/blog/[slug]/view - 记录文章浏览
export const POST = withErrorHandling(async (
  request: NextRequest,
  { params }: RouteParams
) => {
  const { slug } = params
  
  if (!slug) {
    return ApiErrors.validation('Slug is required')
  }
  
  try {
    const body = await request.json()
    
    // 查找文章
    const post = await prisma.blogPost.findUnique({
      where: { slug },
      select: { id: true }
    })
    
    if (!post) {
      return ApiErrors.notFound(`Post with slug "${slug}" not found`)
    }
    
    // 获取IP地址和User Agent
    const ipAddress = body.ipAddress || 
      request.headers.get('x-forwarded-for')?.split(',')[0] ||
      request.headers.get('x-real-ip') ||
      'unknown'
    
    const userAgent = request.headers.get('user-agent') || 'unknown'
    
    // 检查是否在短时间内重复访问（防止刷量）
    const recentView = await prisma.blogView.findFirst({
      where: {
        postId: post.id,
        ipAddress,
        createdAt: {
          gte: new Date(Date.now() - 5 * 60 * 1000) // 5分钟内
        }
      }
    })
    
    if (recentView) {
      return successResponse(
        { viewed: false, reason: 'Recent view exists' },
        'View not recorded (too recent)'
      )
    }
    
    // 记录浏览
    const view = await prisma.blogView.create({
      data: {
        postId: post.id,
        userId: body.userId || null,
        ipAddress,
        userAgent
      }
    })
    
    // 更新文章浏览计数
    await prisma.blogPost.update({
      where: { id: post.id },
      data: {
        viewCount: {
          increment: 1
        }
      }
    })
    
    return successResponse(
      { 
        viewed: true, 
        viewId: view.id,
        totalViews: await prisma.blogView.count({
          where: { postId: post.id }
        })
      },
      'View recorded successfully'
    )
    
  } catch (error) {
    if (error instanceof SyntaxError) {
      return ApiErrors.invalidJson('Invalid JSON in request body')
    }
    throw error
  }
})
