// 单篇博客文章API路由
// Single Blog Post API Routes

import { NextRequest } from 'next/server'
import { prisma, PostStatus } from '@/lib/prisma'
import {
  successResponse,
  ApiErrors,
  withErrorHandling,
  parseJsonField,
  stringifyJsonField
} from '@/lib/api-response'

interface RouteParams {
  params: {
    slug: string
  }
}

// GET /api/blog/[slug] - 获取单篇文章详情
export const GET = withErrorHandling(async (
  request: NextRequest,
  { params }: RouteParams
) => {
  const { slug } = params
  
  if (!slug) {
    return ApiErrors.validation('Slug is required')
  }
  
  // 查询文章详情（包含关联数据）
  const post = await prisma.blogPost.findUnique({
    where: { slug },
    include: {
      views: {
        select: {
          id: true,
          createdAt: true,
          ipAddress: true
        }
      },
      comments: {
        where: { isApproved: true },
        include: {
          user: {
            select: {
              id: true,
              username: true,
              avatar: true
            }
          },
          replies: {
            where: { isApproved: true },
            include: {
              user: {
                select: {
                  id: true,
                  username: true,
                  avatar: true
                }
              }
            }
          }
        },
        orderBy: { createdAt: 'desc' }
      },
      favorites: {
        include: {
          user: {
            select: {
              id: true,
              username: true
            }
          }
        }
      }
    }
  })
  
  if (!post) {
    return ApiErrors.notFound(`Post with slug "${slug}" not found`)
  }
  
  // 检查文章状态（只有已发布的文章才能被访问，除非是管理员）
  if (post.status !== PostStatus.PUBLISHED) {
    return ApiErrors.notFound('Post not available')
  }
  
  // 格式化响应数据
  const formattedPost = {
    ...post,
    tags: parseJsonField<string[]>(post.tags) || [],
    keywords: parseJsonField<string[]>(post.keywords) || [],
    metadata: parseJsonField(post.metadata) || {},
    commentsCount: post.comments.length,
    favoritesCount: post.favorites.length,
    uniqueViewsCount: new Set(post.views.map(v => v.ipAddress)).size
  }
  
  return successResponse(formattedPost, 'Post retrieved successfully')
})

// PUT /api/blog/[slug] - 更新文章
export const PUT = withErrorHandling(async (
  request: NextRequest,
  { params }: RouteParams
) => {
  const { slug } = params
  
  if (!slug) {
    return ApiErrors.validation('Slug is required')
  }
  
  try {
    const body = await request.json()
    
    // 查找现有文章
    const existingPost = await prisma.blogPost.findUnique({
      where: { slug }
    })
    
    if (!existingPost) {
      return ApiErrors.notFound(`Post with slug "${slug}" not found`)
    }
    
    // 准备更新数据
    const updateData: any = {}
    
    if (body.title) updateData.title = body.title
    if (body.content) {
      updateData.content = body.content
      // 重新计算阅读时间
      updateData.readingTime = Math.ceil(body.content.length / 250 / 4)
    }
    if (body.excerpt) updateData.excerpt = body.excerpt
    if (body.coverImage !== undefined) updateData.coverImage = body.coverImage
    if (body.category) updateData.category = body.category
    if (body.seoTitle !== undefined) updateData.seoTitle = body.seoTitle
    if (body.seoDescription !== undefined) updateData.seoDescription = body.seoDescription
    
    // 处理JSON字段
    if (Array.isArray(body.tags)) {
      updateData.tags = stringifyJsonField(body.tags)
    }
    if (Array.isArray(body.keywords)) {
      updateData.keywords = stringifyJsonField(body.keywords)
    }
    if (body.metadata) {
      updateData.metadata = stringifyJsonField(body.metadata)
    }
    
    // 处理状态变更
    if (body.status && body.status !== existingPost.status) {
      updateData.status = body.status
      
      // 如果从非发布状态变为发布状态，设置发布时间
      if (body.status === PostStatus.PUBLISHED && existingPost.status !== PostStatus.PUBLISHED) {
        updateData.publishedAt = new Date()
      }
    }
    
    // 更新文章
    const updatedPost = await prisma.blogPost.update({
      where: { slug },
      data: updateData
    })
    
    // 格式化响应
    const formattedPost = {
      ...updatedPost,
      tags: parseJsonField<string[]>(updatedPost.tags) || [],
      keywords: parseJsonField<string[]>(updatedPost.keywords) || [],
      metadata: parseJsonField(updatedPost.metadata) || {}
    }
    
    return successResponse(formattedPost, 'Post updated successfully')
    
  } catch (error) {
    if (error instanceof SyntaxError) {
      return ApiErrors.invalidJson('Invalid JSON in request body')
    }
    throw error
  }
})

// DELETE /api/blog/[slug] - 删除文章（软删除）
export const DELETE = withErrorHandling(async (
  request: NextRequest,
  { params }: RouteParams
) => {
  const { slug } = params
  
  if (!slug) {
    return ApiErrors.validation('Slug is required')
  }
  
  // 查找文章
  const post = await prisma.blogPost.findUnique({
    where: { slug }
  })
  
  if (!post) {
    return ApiErrors.notFound(`Post with slug "${slug}" not found`)
  }
  
  // 软删除（更新状态为DELETED）
  await prisma.blogPost.update({
    where: { slug },
    data: {
      status: PostStatus.DELETED
    }
  })
  
  return successResponse(
    { slug, deleted: true },
    'Post deleted successfully'
  )
})
