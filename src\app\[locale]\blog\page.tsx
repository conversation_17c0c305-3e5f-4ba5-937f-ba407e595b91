import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { getTranslations } from 'next-intl/server';
import { Suspense } from 'react';
import Link from 'next/link';
import { Search, TrendingUp, Calendar, Tag, Mail } from 'lucide-react';
import { BlogList, BlogListSkeleton } from '@/components/blog';
import { BlogPost, BlogCategory, Locale } from '@/types';
import { cn } from '@/lib/utils';
import { prisma } from '@/lib/prisma';

interface BlogPageProps {
  params: {
    locale: string;
  };
  searchParams: {
    page?: string;
    category?: string;
    tag?: string;
    search?: string;
  };
}

export async function generateMetadata({ params }: BlogPageProps): Promise<Metadata> {
  const t = await getTranslations({ locale: params.locale, namespace: 'blog' });
  
  return {
    title: t('title'),
    description: t('description'),
    keywords: [
      t('keywords.mystical'),
      t('keywords.tarot'),
      t('keywords.astrology'),
      t('keywords.numerology'),
      t('keywords.crystal'),
      t('keywords.palmistry'),
      t('keywords.dreams'),
    ],
    openGraph: {
      title: t('title'),
      description: t('description'),
      type: 'website',
      locale: params.locale,
    },
  };
}

// 实际的数据库查询函数
async function getBlogPosts(
  locale: Locale,
  page: number = 1,
  limit: number = 12,
  filters?: {
    category?: string;
    tag?: string;
    search?: string;
  }
): Promise<{
  posts: BlogPost[];
  featuredPost?: BlogPost;
  totalPages: number;
  categories: BlogCategory[];
}> {
  try {
    // 构建查询条件
    const where: any = {
      locale,
      status: 'published'
    };

    if (filters?.category) {
      where.category = filters.category;
    }

    if (filters?.search) {
      where.OR = [
        { title: { contains: filters.search, mode: 'insensitive' } },
        { content: { contains: filters.search, mode: 'insensitive' } },
        { excerpt: { contains: filters.search, mode: 'insensitive' } }
      ];
    }

    // 计算偏移量
    const skip = (page - 1) * limit;

    // 查询文章
    const [posts, total, featuredPost] = await Promise.all([
      prisma.blogPost.findMany({
        where,
        orderBy: [
          { publishedAt: 'desc' },
          { createdAt: 'desc' }
        ],
        skip,
        take: limit,
      }),
      prisma.blogPost.count({ where }),
      // 获取特色文章（featured=true的最新文章）
      prisma.blogPost.findFirst({
        where: {
          locale,
          status: 'published',
          featured: true
        },
        orderBy: { publishedAt: 'desc' }
      })
    ]);

    // 解析JSON字段
    const formattedPosts = posts.map(post => ({
      ...post,
      tags: JSON.parse(post.tags || '[]'),
      keywords: JSON.parse(post.keywords || '[]'),
      metadata: JSON.parse(post.metadata || '{}'),
      author: JSON.parse(post.metadata || '{}').author || {
        name: '神秘作者',
        avatar: '/images/avatars/default.jpg'
      }
    }));

    const formattedFeaturedPost = featuredPost ? {
      ...featuredPost,
      tags: JSON.parse(featuredPost.tags || '[]'),
      keywords: JSON.parse(featuredPost.keywords || '[]'),
      metadata: JSON.parse(featuredPost.metadata || '{}'),
      author: JSON.parse(featuredPost.metadata || '{}').author || {
        name: '神秘作者',
        avatar: '/images/avatars/default.jpg'
      }
    } : undefined;

    // 模拟分类数据（实际项目中应该从数据库获取）
    const categories: BlogCategory[] = [
      { id: 'tarot', name: '塔罗牌', slug: 'tarot', postCount: 0 },
      { id: 'astrology', name: '星座占星', slug: 'astrology', postCount: 0 },
      { id: 'numerology', name: '数字命理', slug: 'numerology', postCount: 0 },
      { id: 'crystal', name: '水晶能量', slug: 'crystal', postCount: 0 },
      { id: 'palmistry', name: '手相解读', slug: 'palmistry', postCount: 0 },
      { id: 'dreams', name: '梦境解析', slug: 'dreams', postCount: 0 }
    ];

    return {
      posts: formattedPosts as BlogPost[],
      featuredPost: formattedFeaturedPost as BlogPost | undefined,
      totalPages: Math.ceil(total / limit),
      categories,
    };
  } catch (error) {
    console.error('Error fetching blog posts:', error);
    return {
      posts: [],
      featuredPost: undefined,
      totalPages: 1,
      categories: [],
    };
  }
}

export default async function BlogPage({ params, searchParams }: BlogPageProps) {
  const t = await getTranslations({ locale: params.locale, namespace: 'blog' });
  const locale = params.locale as Locale;

  const page = parseInt(searchParams.page || '1', 10);
  const { posts, featuredPost, totalPages, categories } = await getBlogPosts(
    locale,
    page,
    12,
    {
      category: searchParams.category,
      tag: searchParams.tag,
      search: searchParams.search,
    }
  );

  return (
    <div className="min-h-screen bg-white dark:bg-dark-900">
      {/* 页面头部 - Medium风格简化设计 */}
      <header className="py-16 bg-white dark:bg-dark-900 border-b border-mystical-200 dark:border-dark-700">
        <div className="max-w-6xl mx-auto px-4">
          <div className="text-center mb-12">
            <h1 className={cn(
              "text-4xl md:text-5xl lg:text-6xl font-bold font-serif",
              "text-mystical-900 dark:text-white mb-6",
              "leading-tight tracking-[-0.02em]"
            )}>
              玄学智慧博客
            </h1>
            <p className={cn(
              "text-lg md:text-xl text-mystical-600 dark:text-mystical-300",
              "max-w-3xl mx-auto leading-relaxed"
            )}>
              探索塔罗、星座、数字命理等神秘学知识，获得AI智能解读和人生指导
            </p>
          </div>

          {/* 分类导航 - 简化设计 */}
          <nav className="flex flex-wrap justify-center gap-2">
            <Link
              href="/blog"
              className={cn(
                "px-4 py-2 text-sm font-medium rounded-full transition-all duration-200",
                "text-mystical-600 dark:text-mystical-400",
                "hover:text-mystical-700 dark:hover:text-mystical-300",
                "hover:bg-mystical-50 dark:hover:bg-dark-800",
                !searchParams.category && "bg-mystical-100 dark:bg-dark-700 text-mystical-700 dark:text-mystical-300"
              )}
            >
              全部文章
            </Link>
            {categories.map((category) => (
              <Link
                key={category.id}
                href={`/blog?category=${category.slug}`}
                className={cn(
                  "px-4 py-2 text-sm font-medium rounded-full transition-all duration-200",
                  "text-mystical-600 dark:text-mystical-400",
                  "hover:text-mystical-700 dark:hover:text-mystical-300",
                  "hover:bg-mystical-50 dark:hover:bg-dark-800",
                  searchParams.category === category.slug && "bg-mystical-100 dark:bg-dark-700 text-mystical-700 dark:text-mystical-300"
                )}
              >
                {category.name}
              </Link>
            ))}
          </nav>
        </div>
      </header>

      {/* 主要内容区域 - Medium布局 */}
      <main className="max-w-6xl mx-auto px-4 py-12">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
          {/* 主内容区 */}
          <div className="lg:col-span-2">
            <Suspense fallback={<BlogListSkeleton showFeatured={!!featuredPost} columns={1} />}>
              <BlogList
                posts={posts}
                featuredPost={featuredPost}
                showFeatured={page === 1} // 只在第一页显示特色文章
                layout="list"
                columns={1}
              />
            </Suspense>
          </div>

          {/* 侧边栏 */}
          <aside className="lg:col-span-1">
            <BlogSidebar categories={categories} />
          </aside>
        </div>
      </main>
    </div>
  );
}

// 博客侧边栏组件 - Medium风格
function BlogSidebar({ categories }: { categories: BlogCategory[] }) {
  return (
    <div className="sticky top-8 space-y-8">
      {/* 搜索框 */}
      <div className="bg-white dark:bg-dark-800 p-6 rounded-xl border border-mystical-200 dark:border-dark-700">
        <div className="flex items-center gap-2 mb-4">
          <Search className="w-5 h-5 text-mystical-600 dark:text-mystical-400" />
          <h3 className="text-lg font-semibold text-mystical-900 dark:text-white">
            搜索文章
          </h3>
        </div>
        <div className="relative">
          <input
            type="text"
            placeholder="输入关键词搜索..."
            className={cn(
              "w-full px-4 py-3 pl-10 text-sm rounded-lg transition-all duration-200",
              "border border-mystical-200 dark:border-dark-600",
              "bg-white dark:bg-dark-700",
              "text-mystical-800 dark:text-mystical-200",
              "placeholder-mystical-400 dark:placeholder-mystical-500",
              "focus:outline-none focus:ring-2 focus:ring-mystical-500 focus:border-transparent",
              "hover:border-mystical-300 dark:hover:border-dark-500"
            )}
          />
          <Search className="absolute left-3 top-3.5 w-4 h-4 text-mystical-400" />
        </div>
      </div>

      {/* 热门分类 */}
      <div className="bg-white dark:bg-dark-800 p-6 rounded-xl border border-mystical-200 dark:border-dark-700">
        <div className="flex items-center gap-2 mb-4">
          <TrendingUp className="w-5 h-5 text-mystical-600 dark:text-mystical-400" />
          <h3 className="text-lg font-semibold text-mystical-900 dark:text-white">
            热门分类
          </h3>
        </div>
        <div className="space-y-1">
          {categories.slice(0, 6).map((category) => (
            <Link
              key={category.id}
              href={`/blog?category=${category.slug}`}
              className={cn(
                "flex items-center justify-between p-3 rounded-lg transition-all duration-200",
                "hover:bg-mystical-50 dark:hover:bg-dark-700",
                "group"
              )}
            >
              <span className="text-sm font-medium text-mystical-700 dark:text-mystical-300 group-hover:text-mystical-800 dark:group-hover:text-mystical-200">
                {category.name}
              </span>
              <span className="text-xs text-mystical-500 dark:text-mystical-400 bg-mystical-100 dark:bg-dark-600 px-2 py-1 rounded-full font-medium">
                {category.postCount}
              </span>
            </Link>
          ))}
        </div>
      </div>

      {/* 热门标签 */}
      <div className="bg-white dark:bg-dark-800 p-6 rounded-xl border border-mystical-200 dark:border-dark-700">
        <div className="flex items-center gap-2 mb-4">
          <Tag className="w-5 h-5 text-mystical-600 dark:text-mystical-400" />
          <h3 className="text-lg font-semibold text-mystical-900 dark:text-white">
            热门标签
          </h3>
        </div>
        <div className="flex flex-wrap gap-2">
          {['塔罗牌', '星座运势', '数字命理', '水晶疗愈', '手相解读', '梦境解析', '冥想', '能量清理'].map((tag) => (
            <Link
              key={tag}
              href={`/blog?tag=${encodeURIComponent(tag)}`}
              className={cn(
                "px-3 py-1 text-xs font-medium rounded-full transition-all duration-200",
                "bg-mystical-100 dark:bg-dark-700 text-mystical-600 dark:text-mystical-400",
                "hover:bg-mystical-200 dark:hover:bg-dark-600 hover:text-mystical-700 dark:hover:text-mystical-300"
              )}
            >
              #{tag}
            </Link>
          ))}
        </div>
      </div>

      {/* 订阅表单 */}
      <div className="bg-gradient-to-br from-mystical-50 to-gold-50 dark:from-dark-800 dark:to-dark-700 p-6 rounded-xl border border-mystical-200 dark:border-dark-600">
        <div className="flex items-center gap-2 mb-3">
          <Mail className="w-5 h-5 text-mystical-600 dark:text-mystical-400" />
          <h3 className="text-lg font-semibold text-mystical-900 dark:text-white">
            订阅更新
          </h3>
        </div>
        <p className="text-sm text-mystical-600 dark:text-mystical-300 mb-4 leading-relaxed">
          获取最新的玄学知识、测试内容和深度解读文章
        </p>
        <form className="space-y-3">
          <input
            type="email"
            placeholder="输入邮箱地址"
            className={cn(
              "w-full px-4 py-3 text-sm rounded-lg transition-all duration-200",
              "border border-mystical-200 dark:border-dark-600",
              "bg-white dark:bg-dark-700",
              "text-mystical-800 dark:text-mystical-200",
              "placeholder-mystical-400 dark:placeholder-mystical-500",
              "focus:outline-none focus:ring-2 focus:ring-mystical-500 focus:border-transparent"
            )}
          />
          <button
            type="submit"
            className={cn(
              "w-full px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200",
              "bg-mystical-500 text-white",
              "hover:bg-mystical-600 hover:shadow-mystical",
              "focus:outline-none focus:ring-2 focus:ring-mystical-500 focus:ring-offset-2"
            )}
          >
            免费订阅
          </button>
        </form>
      </div>
    </div>
  );
}
