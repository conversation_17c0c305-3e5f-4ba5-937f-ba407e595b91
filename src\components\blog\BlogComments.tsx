'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import { MessageCircle, Heart, Reply, MoreHorizontal } from 'lucide-react';
import { cn } from '@/lib/utils';
import { BlogPost } from '@/types';

interface Comment {
  id: string;
  content: string;
  author: {
    name: string;
    avatar?: string;
  };
  createdAt: Date;
  likeCount: number;
  isLiked: boolean;
  replies?: Comment[];
}

interface BlogCommentsProps {
  post: BlogPost;
  className?: string;
}

export function BlogComments({ post, className }: BlogCommentsProps) {
  const [comments, setComments] = useState<Comment[]>([]);
  const [newComment, setNewComment] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmitComment = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newComment.trim() || isSubmitting) return;

    setIsSubmitting(true);
    try {
      // TODO: 实际的API调用
      // const comment = await submitComment(post.id, newComment);
      // setComments(prev => [comment, ...prev]);
      setNewComment('');
    } catch (error) {
      console.error('Failed to submit comment:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  return (
    <section id="comments" className={cn('mt-16', className)}>
      {/* 评论标题 */}
      <div className="flex items-center justify-between mb-8">
        <h3 className="text-2xl font-bold text-mystical-900 dark:text-white">
          评论 ({comments.length})
        </h3>
        <div className="flex items-center gap-2 text-sm text-mystical-500 dark:text-mystical-400">
          <MessageCircle className="w-4 h-4" />
          <span>参与讨论</span>
        </div>
      </div>

      {/* 评论表单 */}
      <form onSubmit={handleSubmitComment} className="mb-12">
        <div className="mb-4">
          <textarea
            value={newComment}
            onChange={(e) => setNewComment(e.target.value)}
            placeholder="分享你的想法..."
            rows={4}
            className={cn(
              'w-full px-4 py-3 text-base',
              'border border-mystical-200 dark:border-dark-600 rounded-lg',
              'bg-white dark:bg-dark-800',
              'text-mystical-800 dark:text-mystical-200',
              'placeholder-mystical-400 dark:placeholder-mystical-500',
              'focus:outline-none focus:ring-2 focus:ring-mystical-500 focus:border-transparent',
              'resize-vertical transition-colors'
            )}
          />
        </div>
        <div className="flex items-center justify-between">
          <p className="text-xs text-mystical-500 dark:text-mystical-400">
            请保持友善和尊重的讨论氛围
          </p>
          <button
            type="submit"
            disabled={!newComment.trim() || isSubmitting}
            className={cn(
              'px-6 py-2 text-sm font-medium rounded-lg transition-all duration-200',
              'bg-mystical-500 text-white',
              'hover:bg-mystical-600 hover:shadow-mystical',
              'disabled:opacity-50 disabled:cursor-not-allowed',
              'focus:outline-none focus:ring-2 focus:ring-mystical-500 focus:ring-offset-2'
            )}
          >
            {isSubmitting ? '发布中...' : '发布评论'}
          </button>
        </div>
      </form>

      {/* 评论列表 */}
      {comments.length > 0 ? (
        <div className="space-y-8">
          {comments.map((comment) => (
            <CommentItem key={comment.id} comment={comment} />
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <MessageCircle className="w-12 h-12 text-mystical-300 dark:text-mystical-600 mx-auto mb-4" />
          <p className="text-mystical-600 dark:text-mystical-400 mb-2">
            还没有评论
          </p>
          <p className="text-sm text-mystical-500 dark:text-mystical-500">
            成为第一个分享想法的人吧！
          </p>
        </div>
      )}
    </section>
  );
}

function CommentItem({ comment }: { comment: Comment }) {
  const [isLiked, setIsLiked] = useState(comment.isLiked);
  const [likeCount, setLikeCount] = useState(comment.likeCount);
  const [showReplies, setShowReplies] = useState(false);

  const handleLike = async () => {
    try {
      const newLiked = !isLiked;
      setIsLiked(newLiked);
      setLikeCount(prev => newLiked ? prev + 1 : prev - 1);
      // TODO: API调用
    } catch (error) {
      setIsLiked(!isLiked);
      setLikeCount(prev => isLiked ? prev + 1 : prev - 1);
    }
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('zh-CN', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  return (
    <div className="group">
      <div className="flex gap-4">
        {/* 头像 */}
        <div className="flex-shrink-0">
          {comment.author.avatar ? (
            <Image
              src={comment.author.avatar}
              alt={comment.author.name}
              width={40}
              height={40}
              className="rounded-full border-2 border-mystical-200 dark:border-dark-600"
            />
          ) : (
            <div className="w-10 h-10 bg-mystical-100 dark:bg-dark-700 rounded-full flex items-center justify-center">
              <span className="text-sm font-medium text-mystical-600 dark:text-mystical-400">
                {comment.author.name.charAt(0).toUpperCase()}
              </span>
            </div>
          )}
        </div>

        {/* 评论内容 */}
        <div className="flex-1 min-w-0">
          <div className="bg-mystical-50 dark:bg-dark-800 rounded-lg p-4 border border-mystical-200 dark:border-dark-600">
            <div className="flex items-center justify-between mb-2">
              <h4 className="font-semibold text-mystical-900 dark:text-white">
                {comment.author.name}
              </h4>
              <div className="flex items-center gap-2">
                <span className="text-xs text-mystical-500 dark:text-mystical-400">
                  {formatDate(comment.createdAt)}
                </span>
                <button className="opacity-0 group-hover:opacity-100 transition-opacity">
                  <MoreHorizontal className="w-4 h-4 text-mystical-400" />
                </button>
              </div>
            </div>
            <p className="text-mystical-700 dark:text-mystical-300 leading-relaxed">
              {comment.content}
            </p>
          </div>

          {/* 评论操作 */}
          <div className="flex items-center gap-4 mt-3 ml-4">
            <button
              onClick={handleLike}
              className={cn(
                'flex items-center gap-1 text-xs transition-colors',
                isLiked
                  ? 'text-red-500'
                  : 'text-mystical-500 dark:text-mystical-400 hover:text-red-500'
              )}
            >
              <Heart className={cn('w-3 h-3', isLiked && 'fill-current')} />
              <span>{likeCount > 0 ? likeCount : '点赞'}</span>
            </button>
            <button className="flex items-center gap-1 text-xs text-mystical-500 dark:text-mystical-400 hover:text-mystical-600 dark:hover:text-mystical-300 transition-colors">
              <Reply className="w-3 h-3" />
              <span>回复</span>
            </button>
          </div>

          {/* 回复 */}
          {comment.replies && comment.replies.length > 0 && (
            <div className="mt-4 ml-4">
              <button
                onClick={() => setShowReplies(!showReplies)}
                className="text-xs text-mystical-600 dark:text-mystical-400 hover:text-mystical-700 dark:hover:text-mystical-300 transition-colors mb-3"
              >
                {showReplies ? '隐藏' : '查看'} {comment.replies.length} 条回复
              </button>
              {showReplies && (
                <div className="space-y-4 border-l-2 border-mystical-200 dark:border-dark-600 pl-4">
                  {comment.replies.map((reply) => (
                    <CommentItem key={reply.id} comment={reply} />
                  ))}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
