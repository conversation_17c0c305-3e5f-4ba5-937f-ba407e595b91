// AI内容生成服务 - 基于03-blog-management-rules.md规范

import { BlogPost, BlogPostStatus, Locale, AIGeneratedMetadata } from '@/types';

export interface AIContentRequest {
  topic: string;
  category: string;
  locale: Locale;
  targetLength?: number; // 目标字数
  keywords?: string[];
  tone?: 'professional' | 'casual' | 'mystical' | 'educational';
  includeImages?: boolean;
}

export interface AIGeneratedContent {
  title: string;
  slug: string;
  content: string; // HTML格式
  excerpt: string;
  coverImage?: string;
  tags: string[];
  seoTitle: string;
  seoDescription: string;
  keywords: string[];
  readingTime: number;
  aiMetadata: AIGeneratedMetadata;
}

export interface BatchGenerationRequest {
  topics: string[];
  category: string;
  locale: Locale;
  commonSettings: Partial<AIContentRequest>;
}

export interface BatchGenerationResult {
  total: number;
  successful: number;
  failed: number;
  results: Array<{
    topic: string;
    success: boolean;
    content?: AIGeneratedContent;
    error?: string;
  }>;
}

// AI内容生成器类
export class AIContentGenerator {
  private apiKey: string;
  private baseUrl: string;

  constructor() {
    this.apiKey = process.env.QWEN_API_KEY || '';
    this.baseUrl = process.env.QWEN_API_URL || 'https://dashscope.aliyuncs.com/api/v1';
  }

  // 单篇文章生成
  async generateContent(request: AIContentRequest): Promise<AIGeneratedContent> {
    const startTime = Date.now();
    
    try {
      // 构建提示词
      const prompt = this.buildPrompt(request);
      
      // 调用AI API
      const response = await this.callAI(prompt, request.locale);
      
      // 解析AI响应
      const parsedContent = this.parseAIResponse(response);
      
      // 生成SEO优化内容
      const seoOptimized = await this.optimizeSEO(parsedContent, request);
      
      // 计算阅读时间
      const readingTime = this.calculateReadingTime(seoOptimized.content);
      
      // 生成slug
      const slug = this.generateSlug(seoOptimized.title, request.locale);
      
      const processingTime = Date.now() - startTime;
      
      return {
        ...seoOptimized,
        slug,
        readingTime,
        aiMetadata: {
          model: 'qwen-turbo',
          generatedAt: new Date(),
          prompt: prompt.substring(0, 500), // 只保存前500字符
          confidence: 0.85, // 模拟置信度
          processingTime,
          qualityScore: await this.calculateQualityScore(seoOptimized.content)
        }
      };
    } catch (error) {
      throw new Error(`AI content generation failed: ${error.message}`);
    }
  }

  // 批量生成
  async batchGenerate(request: BatchGenerationRequest): Promise<BatchGenerationResult> {
    const results: BatchGenerationResult['results'] = [];
    
    for (const topic of request.topics) {
      try {
        const contentRequest: AIContentRequest = {
          topic,
          category: request.category,
          locale: request.locale,
          ...request.commonSettings
        };
        
        const content = await this.generateContent(contentRequest);
        
        results.push({
          topic,
          success: true,
          content
        });
        
        // 批量生成间隔，避免API限制
        await this.delay(1000);
        
      } catch (error) {
        results.push({
          topic,
          success: false,
          error: error.message
        });
      }
    }
    
    return {
      total: request.topics.length,
      successful: results.filter(r => r.success).length,
      failed: results.filter(r => !r.success).length,
      results
    };
  }

  // 构建AI提示词
  private buildPrompt(request: AIContentRequest): string {
    const { topic, category, locale, targetLength = 1500, tone = 'mystical' } = request;
    
    const prompts = {
      'zh-CN': `请为玄学网站创作一篇关于"${topic}"的专业博客文章。

要求：
- 分类：${category}
- 字数：约${targetLength}字
- 语调：${tone === 'mystical' ? '神秘而专业' : tone === 'educational' ? '教育性' : '专业'}
- 包含实用的指导和深度见解
- 结构清晰，包含小标题
- 适合SEO优化

请按以下JSON格式返回：
{
  "title": "吸引人的标题",
  "content": "HTML格式的文章内容，包含适当的标题标签",
  "excerpt": "150字以内的摘要",
  "tags": ["相关标签1", "相关标签2"],
  "seoTitle": "SEO优化的标题",
  "seoDescription": "SEO描述",
  "keywords": ["关键词1", "关键词2"]
}`,
      
      'en': `Create a professional blog article about "${topic}" for a mystical website.

Requirements:
- Category: ${category}
- Length: approximately ${targetLength} words
- Tone: ${tone}
- Include practical guidance and deep insights
- Clear structure with subheadings
- SEO optimized

Return in JSON format:
{
  "title": "Engaging title",
  "content": "HTML formatted article content with proper heading tags",
  "excerpt": "Summary under 150 words",
  "tags": ["tag1", "tag2"],
  "seoTitle": "SEO optimized title",
  "seoDescription": "SEO description",
  "keywords": ["keyword1", "keyword2"]
}`
    };
    
    return prompts[locale] || prompts['en'];
  }

  // 调用AI API
  private async callAI(prompt: string, locale: Locale): Promise<string> {
    const response = await fetch(`${this.baseUrl}/services/aigc/text-generation/generation`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'qwen-turbo',
        input: {
          messages: [
            {
              role: 'user',
              content: prompt
            }
          ]
        },
        parameters: {
          temperature: 0.7,
          max_tokens: 3000,
          top_p: 0.9
        }
      })
    });

    if (!response.ok) {
      throw new Error(`AI API request failed: ${response.statusText}`);
    }

    const data = await response.json();
    return data.output?.text || data.output?.choices?.[0]?.message?.content || '';
  }

  // 解析AI响应
  private parseAIResponse(response: string): Partial<AIGeneratedContent> {
    try {
      // 尝试解析JSON响应
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
      
      // 如果不是JSON格式，尝试解析文本格式
      return this.parseTextResponse(response);
    } catch (error) {
      throw new Error(`Failed to parse AI response: ${error.message}`);
    }
  }

  // 解析文本格式响应
  private parseTextResponse(response: string): Partial<AIGeneratedContent> {
    const lines = response.split('\n');
    const result: Partial<AIGeneratedContent> = {};
    
    // 简单的文本解析逻辑
    result.title = lines[0]?.replace(/^#+\s*/, '') || '未命名文章';
    result.content = `<div>${response.replace(/\n/g, '</p><p>')}</div>`;
    result.excerpt = lines.slice(1, 3).join(' ').substring(0, 150);
    result.tags = ['AI生成', '玄学'];
    result.seoTitle = result.title;
    result.seoDescription = result.excerpt;
    result.keywords = ['玄学', 'AI'];
    
    return result;
  }

  // SEO优化
  private async optimizeSEO(
    content: Partial<AIGeneratedContent>, 
    request: AIContentRequest
  ): Promise<Omit<AIGeneratedContent, 'aiMetadata' | 'slug' | 'readingTime'>> {
    // 这里可以添加更多SEO优化逻辑
    return {
      title: content.title || '未命名文章',
      content: content.content || '',
      excerpt: content.excerpt || '',
      coverImage: content.coverImage,
      tags: content.tags || [],
      seoTitle: content.seoTitle || content.title || '',
      seoDescription: content.seoDescription || content.excerpt || '',
      keywords: content.keywords || []
    };
  }

  // 计算阅读时间
  private calculateReadingTime(content: string): number {
    const wordsPerMinute = 250; // 中文约250字/分钟
    const textContent = content.replace(/<[^>]*>/g, ''); // 移除HTML标签
    const wordCount = textContent.length;
    return Math.ceil(wordCount / wordsPerMinute);
  }

  // 生成slug
  private generateSlug(title: string, locale: Locale): string {
    if (locale.startsWith('zh')) {
      // 中文标题转拼音或使用时间戳
      return `article-${Date.now()}`;
    }
    
    return title
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  }

  // 计算内容质量分数
  private async calculateQualityScore(content: string): Promise<number> {
    // 简单的质量评估逻辑
    let score = 50; // 基础分数
    
    // 长度检查
    if (content.length > 1000) score += 20;
    if (content.length > 2000) score += 10;
    
    // 结构检查
    if (content.includes('<h2>') || content.includes('<h3>')) score += 10;
    if (content.includes('<ul>') || content.includes('<ol>')) score += 5;
    
    // 关键词密度检查
    const keywordCount = (content.match(/玄学|塔罗|星座|命理/g) || []).length;
    if (keywordCount > 3) score += 5;
    
    return Math.min(score, 100);
  }

  // 延迟函数
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
